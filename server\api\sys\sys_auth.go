package sys

import (
	"server/core/dbpool"
	"server/global"
	"server/model/request"
	"server/model/response"
	"server/model/sys"
	"server/utils"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/mojocn/base64Captcha"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type AuthApi struct{}

var store = base64Captcha.DefaultMemStore

func (a *AuthApi) Login(c *gin.Context) {
	var l request.LoginReq
	err := c.ShouldBindJSON(&l)
	key := c.ClientIP()
	if err != nil {
		response.LoginFailedWithMessage(err.Error(), c)
	}

	err = utils.Verify(l, utils.LoginVerify)
	if err != nil {
		response.LoginFailedWithMessage(err.Error(), c)
		return
	}

	// 判断验证码是否开启
	openCaptcha := global.CONFIG.Captcha.OpenCaptcha               // 是否开启验证码
	maxFailedAttempts := global.CONFIG.Captcha.MaxFailedAttempts   // 最大失败尝试次数
	openCaptchaTimeOut := global.CONFIG.Captcha.OpenCaptchaTimeOut // 缓存超时时间

	// 获取当前IP的失败次数
	v, ok := global.BlackCache.Get(key)
	if !ok {
		global.BlackCache.Set(key, 0, time.Second*time.Duration(openCaptchaTimeOut))
		v = 0
	}

	failedCount := interfaceToInt(v)

	// 检查是否超过最大失败次数，如果超过则直接拒绝登录
	if failedCount >= maxFailedAttempts {
		global.LOG.Warn("IP登录失败次数过多，暂时禁止登录", zap.String("ip", key), zap.Int("failedCount", failedCount))
		response.LoginFailedWithMessage("登录失败次数过多，请稍后再试", c)
		return
	}

	// 判断是否需要验证码：开启验证码 且 失败次数大于global.CONFIG.Captcha.MaxFailedAttempts
	needCaptcha := openCaptcha && failedCount > global.CONFIG.Captcha.MaxFailedAttempts
	if !needCaptcha {
		response.ErrorWithDetailed(response.SysCaptchaResponse{
			CaptchaId:     "",
			PicPath:       "",
			CaptchaLength: global.CONFIG.Captcha.KeyLong,
			OpenCaptcha:   needCaptcha,
		}, "验证码获取失败: 登录尝试过多", c)
		return
	}
	// 如果不需要验证码，或者验证码验证通过，则继续登录流程
	if !needCaptcha || (l.CaptchaId != "" && l.Captcha != "" && store.Verify(l.CaptchaId, l.Captcha, true)) {
		u := &sys.SysUser{Username: l.Username, Password: l.Password}
		user, err := userService.Login(u)
		if err != nil {
			global.LOG.Error("登陆失败! 用户名不存在或者密码错误!", zap.Error(err))
			// 登录失败，增加失败次数
			global.BlackCache.Increment(key, 1)
			response.LoginFailedWithMessage("用户名不存在或者密码错误", c)
			return
		}
		if user.Enable != 1 {
			global.LOG.Error("登陆失败! 用户被禁止登录!")
			// 登录失败，增加失败次数
			global.BlackCache.Increment(key, 1)
			response.LoginFailedWithMessage("用户被禁止登录", c)
			return
		}
		// 登录成功，清除失败次数
		global.BlackCache.Delete(key)
		a.TokenNext(c, *user)
		return
	}
	// 验证码错误，增加失败次数
	global.BlackCache.Increment(key, 1)
	response.LoginFailedWithMessage("验证码错误", c)
}

// TokenNext 登录以后签发jwt
func (a *AuthApi) TokenNext(c *gin.Context, user sys.SysUser) {
	token, claims, err := sys.LoginToken(&user)
	if err != nil {
		global.LOG.Error("获取token失败!", zap.Error(err))
		response.ErrorWithMessage("获取token失败", c)
		return
	}
	sys.SetToken(c, token, int(claims.RegisteredClaims.ExpiresAt.Unix()-time.Now().Unix()))
	response.OkWithDetailed(sys.LoginResponse{
		User:      user,
		Token:     token,
		ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
	}, "登录成功", c)
}

func (b *AuthApi) Captcha(c *gin.Context) {
	// 判断验证码是否开启
	openCaptcha := global.CONFIG.Captcha.OpenCaptcha               // 是否开启验证码
	openCaptchaTimeOut := global.CONFIG.Captcha.OpenCaptchaTimeOut // 缓存超时时间
	key := c.ClientIP()

	// 获取当前IP的失败次数
	v, ok := global.BlackCache.Get(key)
	if !ok {
		global.BlackCache.Set(key, 0, time.Second*time.Duration(openCaptchaTimeOut))
		v = 0
	}

	failedCount := interfaceToInt(v)

	// 判断是否需要显示验证码：开启验证码 且 失败次数大于0
	showCaptcha := openCaptcha && failedCount > global.CONFIG.Captcha.MaxFailedAttempts
	if !showCaptcha {
		response.ErrorWithDetailed(response.SysCaptchaResponse{
			CaptchaId:     "",
			PicPath:       "",
			CaptchaLength: global.CONFIG.Captcha.KeyLong,
			OpenCaptcha:   showCaptcha,
		}, "验证码获取失败: 登录尝试过多", c)
		return
	}
	// 字符,公式,验证码配置
	// 生成默认数字的driver
	driver := base64Captcha.NewDriverDigit(global.CONFIG.Captcha.ImgHeight, global.CONFIG.Captcha.ImgWidth, global.CONFIG.Captcha.KeyLong, 0.7, 80)
	// cp := base64Captcha.NewCaptcha(driver, store.UseWithCtx(c))   // v8下使用redis
	cp := base64Captcha.NewCaptcha(driver, store)
	id, b64s, _, err := cp.Generate()
	if err != nil {
		global.LOG.Error("验证码获取失败!", zap.Error(err))
		response.ErrorWithMessage("验证码获取失败", c)
		return
	}
	response.OkWithDetailed(response.SysCaptchaResponse{
		CaptchaId:     id,
		PicPath:       b64s,
		CaptchaLength: global.CONFIG.Captcha.KeyLong,
		OpenCaptcha:   showCaptcha,
	}, "验证码获取成功", c)
}

func interfaceToInt(v interface{}) (i int) {
	switch v := v.(type) {
	case int:
		i = v
	default:
		i = 0
	}
	return
}

// UpdateUserInfo 更新用户信息
func (a *AuthApi) UpdateUserInfo(c *gin.Context) {
	// 获取当前用户信息
	claims, exists := c.Get("claims")
	if !exists {
		response.ErrorWithMessage("获取用户信息失败", c)
		return
	}
	userClaims := claims.(*sys.CustomClaims)
	userUUID := userClaims.UUID.String()

	// 解析请求
	var req request.UpdateUserInfoReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 调用服务更新用户信息
	updatedUser, err := userService.UpdateUserInfo(userUUID, req)
	if err != nil {
		global.LOG.Error("更新用户信息失败", zap.Error(err))
		response.ErrorWithMessage("更新用户信息失败: "+err.Error(), c)
		return
	}

	// 返回更新后的用户信息
	response.OkWithDetailed(updatedUser, "更新用户信息成功", c)
}

// GetUserInfo 获取当前用户信息
func (a *AuthApi) GetUserInfo(c *gin.Context) {
	claims, exists := c.Get("claims")
	if !exists {
		response.NoAuth("未登录或非法访问", c)
		return
	}

	customClaims, ok := claims.(*sys.CustomClaims)
	if !ok {
		response.NoAuth("token解析失败", c)
		return
	}

	// 🚀 通过用户ID查询用户信息
	var user sys.SysUser
	if err := dbpool.ExecuteDBOperationAsyncAndWait("user_info_get", func(db *gorm.DB) error {
		return db.First(&user, customClaims.BaseClaims.ID).Error
	}); err != nil {
		response.ErrorWithMessage("用户不存在", c)
		return
	}

	// 返回用户信息（不包含密码）
	userInfo := map[string]interface{}{
		"id":       user.ID,
		"username": user.Username,
		"roleName": user.Role.RoleName,
		"enable":   user.Enable,
		"isAdmin":  user.Role.RoleName == "superadmin",
	}

	response.OkWithData(userInfo, c)
}
