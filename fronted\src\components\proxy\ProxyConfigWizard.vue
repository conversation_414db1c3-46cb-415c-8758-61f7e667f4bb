<template>
  <a-modal
    v-model:visible="modalVisible"
    title="代理配置向导"
    width="900px"
    :footer="null"
    @cancel="handleClose"
  >
    <a-steps :current="currentStep" class="wizard-steps">
      <a-step title="选择客户端" description="选择要配置代理的客户端" />
      <a-step title="代理类型" description="选择代理类型和基本配置" />
      <a-step title="高级配置" description="配置高级参数和安全选项" />
      <a-step title="确认配置" description="确认配置信息并创建代理" />
    </a-steps>

    <div class="wizard-content">
      <!-- 步骤1：选择客户端 -->
      <div v-if="currentStep === 0" class="step-content">
        <h3>选择客户端</h3>
        <p class="step-desc">请选择要配置代理的在线客户端</p>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="在线客户端" size="small">
              <a-list
                :data-source="onlineClients"
                :loading="loadingClients"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #avatar>
                        <a-avatar :style="{ backgroundColor: getClientStatusColor(item.status) }">
                          <template #icon><DesktopOutlined /></template>
                        </a-avatar>
                      </template>
                      <template #title>
                        <a-radio
                          :value="item.id"
                          v-model:checked="selectedClientId"
                          @change="handleClientSelect(item)"
                        >
                          {{ item.hostname }}
                        </a-radio>
                      </template>
                      <template #description>
                        <div class="client-info">
                          <a-tag size="small" :color="getOSColor(item.os)">{{ item.os }}</a-tag>
                          <span class="client-ip">{{ item.local_ip }}</span>
                          <a-tag size="small" color="green">在线</a-tag>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </a-card>
          </a-col>
          
          <a-col :span="12">
            <a-card title="客户端详情" size="small">
              <div v-if="selectedClient" class="client-detail">
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item label="主机名">{{ selectedClient.hostname }}</a-descriptions-item>
                  <a-descriptions-item label="操作系统">{{ selectedClient.os }}</a-descriptions-item>
                  <a-descriptions-item label="内网IP">{{ selectedClient.local_ip }}</a-descriptions-item>
                  <a-descriptions-item label="公网IP">{{ selectedClient.public_ip }}</a-descriptions-item>
                  <a-descriptions-item label="CPU架构">{{ selectedClient.arch }}</a-descriptions-item>
                  <a-descriptions-item label="连接时间">{{ formatTime(selectedClient.connected_at) }}</a-descriptions-item>
                </a-descriptions>
                
                <a-divider />
                
                <h4>系统信息</h4>
                <a-row :gutter="8">
                  <a-col :span="8">
                    <a-statistic title="CPU使用率" :value="selectedClient.cpu_usage || 0" suffix="%" />
                  </a-col>
                  <a-col :span="8">
                    <a-statistic title="内存使用率" :value="selectedClient.memory_usage || 0" suffix="%" />
                  </a-col>
                  <a-col :span="8">
                    <a-statistic title="网络延迟" :value="selectedClient.ping || 0" suffix="ms" />
                  </a-col>
                </a-row>
              </div>
              <a-empty v-else description="请选择一个客户端查看详情" />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 步骤2：代理类型配置 -->
      <div v-if="currentStep === 1" class="step-content">
        <h3>代理类型配置</h3>
        <p class="step-desc">选择代理类型并配置基本参数</p>
        
        <a-form
          :model="proxyConfig"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="代理类型">
                <a-radio-group v-model:value="proxyConfig.type" @change="handleTypeChange">
                  <a-radio-button value="forward">
                    <ArrowRightOutlined />
                    正向代理
                  </a-radio-button>
                  <a-radio-button value="reverse">
                    <ArrowLeftOutlined />
                    反向代理
                  </a-radio-button>
                  <a-radio-button value="chain">
                    <NodeIndexOutlined />
                    代理链
                  </a-radio-button>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="代理名称">
                <a-input v-model:value="proxyConfig.name" placeholder="请输入代理名称" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="描述">
            <a-textarea v-model:value="proxyConfig.description" :rows="2" placeholder="请输入代理描述（可选）" />
          </a-form-item>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="监听端口">
                <a-input-number
                  v-model:value="proxyConfig.port"
                  :min="1"
                  :max="65535"
                  placeholder="请输入端口号"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="协议类型">
                <a-select v-model:value="proxyConfig.protocol" placeholder="选择协议">
                  <a-select-option value="socks5">SOCKS5</a-select-option>
                  <a-select-option value="http">HTTP</a-select-option>
                  <a-select-option value="https">HTTPS</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 代理类型特定配置 -->
          <div v-if="proxyConfig.type === 'forward'" class="type-specific-config">
            <a-divider orientation="left">正向代理配置</a-divider>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="目标主机">
                  <a-input v-model:value="proxyConfig.target_host" placeholder="目标主机地址" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="目标端口">
                  <a-input-number
                    v-model:value="proxyConfig.target_port"
                    :min="1"
                    :max="65535"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <div v-if="proxyConfig.type === 'reverse'" class="type-specific-config">
            <a-divider orientation="left">反向代理配置</a-divider>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="后端服务器">
                  <a-input v-model:value="proxyConfig.backend_host" placeholder="后端服务器地址" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="后端端口">
                  <a-input-number
                    v-model:value="proxyConfig.backend_port"
                    :min="1"
                    :max="65535"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item>
              <a-checkbox v-model:checked="proxyConfig.load_balancing">启用负载均衡</a-checkbox>
            </a-form-item>
          </div>

          <div v-if="proxyConfig.type === 'chain'" class="type-specific-config">
            <a-divider orientation="left">代理链配置</a-divider>
            <a-form-item label="代理链策略">
              <a-radio-group v-model:value="proxyConfig.chain_strategy">
                <a-radio value="sequential">顺序连接</a-radio>
                <a-radio value="random">随机选择</a-radio>
                <a-radio value="round_robin">轮询</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item>
              <a-checkbox v-model:checked="proxyConfig.health_check">启用健康检查</a-checkbox>
            </a-form-item>
          </div>
        </a-form>
      </div>

      <!-- 步骤3：高级配置 -->
      <div v-if="currentStep === 2" class="step-content">
        <h3>高级配置</h3>
        <p class="step-desc">配置安全选项、性能参数和访问控制</p>
        
        <a-form
          :model="proxyConfig"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <!-- 认证配置 -->
          <a-divider orientation="left">认证配置</a-divider>
          <a-form-item>
            <a-checkbox v-model:checked="proxyConfig.auth_required">启用认证</a-checkbox>
          </a-form-item>
          
          <div v-if="proxyConfig.auth_required">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="用户名">
                  <a-input v-model:value="proxyConfig.username" placeholder="请输入用户名" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="密码">
                  <a-input-password v-model:value="proxyConfig.password" placeholder="请输入密码" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <!-- 性能配置 -->
          <a-divider orientation="left">性能配置</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="最大连接数">
                <a-input-number
                  v-model:value="proxyConfig.max_connections"
                  :min="1"
                  :max="10000"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="超时时间(秒)">
                <a-input-number
                  v-model:value="proxyConfig.timeout"
                  :min="1"
                  :max="3600"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="缓冲区大小">
                <a-input-number
                  v-model:value="proxyConfig.buffer_size"
                  :min="1024"
                  :max="65536"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="日志级别">
                <a-select v-model:value="proxyConfig.log_level">
                  <a-select-option value="debug">Debug</a-select-option>
                  <a-select-option value="info">Info</a-select-option>
                  <a-select-option value="warn">Warn</a-select-option>
                  <a-select-option value="error">Error</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 访问控制 -->
          <a-divider orientation="left">访问控制</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="允许的IP">
                <a-textarea
                  v-model:value="proxyConfig.allowed_ips"
                  :rows="3"
                  placeholder="每行一个IP地址或CIDR，留空表示允许所有"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="阻止的IP">
                <a-textarea
                  v-model:value="proxyConfig.blocked_ips"
                  :rows="3"
                  placeholder="每行一个IP地址或CIDR"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 速率限制 -->
          <a-divider orientation="left">速率限制</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="请求限制(RPS)">
                <a-input-number
                  v-model:value="proxyConfig.rate_limit_rps"
                  :min="0"
                  placeholder="0表示不限制"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="带宽限制(KB/s)">
                <a-input-number
                  v-model:value="proxyConfig.rate_limit_kbps"
                  :min="0"
                  placeholder="0表示不限制"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 其他选项 -->
          <a-divider orientation="left">其他选项</a-divider>
          <a-space direction="vertical">
            <a-checkbox v-model:checked="proxyConfig.enable_metrics">启用指标收集</a-checkbox>
            <a-checkbox v-model:checked="proxyConfig.enable_logging">启用详细日志</a-checkbox>
            <a-checkbox v-model:checked="proxyConfig.auto_start">创建后自动启动</a-checkbox>
          </a-space>
        </a-form>
      </div>

      <!-- 步骤4：确认配置 -->
      <div v-if="currentStep === 3" class="step-content">
        <h3>确认配置</h3>
        <p class="step-desc">请确认以下配置信息，确认无误后点击创建代理</p>
        
        <a-card title="配置摘要" size="small">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="客户端">{{ selectedClient?.hostname }}</a-descriptions-item>
            <a-descriptions-item label="代理名称">{{ proxyConfig.name }}</a-descriptions-item>
            <a-descriptions-item label="代理类型">
              <a-tag :color="getProxyTypeColor(proxyConfig.type)">
                {{ getProxyTypeText(proxyConfig.type) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="监听端口">{{ proxyConfig.port }}</a-descriptions-item>
            <a-descriptions-item label="协议类型">{{ proxyConfig.protocol?.toUpperCase() }}</a-descriptions-item>
            <a-descriptions-item label="认证">
              <a-tag :color="proxyConfig.auth_required ? 'green' : 'default'">
                {{ proxyConfig.auth_required ? '启用' : '禁用' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="最大连接数">{{ proxyConfig.max_connections }}</a-descriptions-item>
            <a-descriptions-item label="超时时间">{{ proxyConfig.timeout }}秒</a-descriptions-item>
            <a-descriptions-item label="描述" :span="2">{{ proxyConfig.description || '无' }}</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <a-card title="高级配置" size="small" style="margin-top: 16px">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-statistic title="缓冲区大小" :value="proxyConfig.buffer_size" suffix="字节" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="请求限制" :value="proxyConfig.rate_limit_rps || '无限制'" suffix="RPS" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="带宽限制" :value="proxyConfig.rate_limit_kbps || '无限制'" suffix="KB/s" />
            </a-col>
          </a-row>
          
          <a-divider />
          
          <a-space wrap>
            <a-tag v-if="proxyConfig.enable_metrics" color="blue">指标收集</a-tag>
            <a-tag v-if="proxyConfig.enable_logging" color="green">详细日志</a-tag>
            <a-tag v-if="proxyConfig.auto_start" color="orange">自动启动</a-tag>
            <a-tag v-if="proxyConfig.allowed_ips" color="cyan">IP白名单</a-tag>
            <a-tag v-if="proxyConfig.blocked_ips" color="red">IP黑名单</a-tag>
          </a-space>
        </a-card>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="wizard-footer">
      <a-space>
        <a-button v-if="currentStep > 0" @click="prevStep">上一步</a-button>
        <a-button v-if="currentStep < 3" type="primary" @click="nextStep" :disabled="!canProceed">下一步</a-button>
        <a-button v-if="currentStep === 3" type="primary" @click="handleSubmit" :loading="submitting">创建代理</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  DesktopOutlined,
  ArrowRightOutlined,
  ArrowLeftOutlined,
  NodeIndexOutlined
} from '@ant-design/icons-vue'
import { getClientList } from '@/api/client'
import { createProxy } from '@/api/proxy'
import { formatTime } from '@/utils/format'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const modalVisible = ref(false)
const currentStep = ref(0)
const loadingClients = ref(false)
const submitting = ref(false)
const onlineClients = ref([])
const selectedClientId = ref(null)
const selectedClient = ref(null)

// 代理配置
const proxyConfig = reactive({
  name: '',
  description: '',
  type: 'forward',
  protocol: 'socks5',
  port: 8080,
  client_id: null,
  
  // 类型特定配置
  target_host: '',
  target_port: 80,
  backend_host: '',
  backend_port: 80,
  load_balancing: false,
  chain_strategy: 'sequential',
  health_check: false,
  
  // 认证配置
  auth_required: false,
  username: '',
  password: '',
  
  // 性能配置
  max_connections: 100,
  timeout: 30,
  buffer_size: 4096,
  log_level: 'info',
  
  // 访问控制
  allowed_ips: '',
  blocked_ips: '',
  
  // 速率限制
  rate_limit_rps: 0,
  rate_limit_kbps: 0,
  
  // 其他选项
  enable_metrics: true,
  enable_logging: false,
  auto_start: false
})

// 计算属性
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return selectedClientId.value !== null
    case 1:
      return proxyConfig.name && proxyConfig.port && proxyConfig.type
    case 2:
      if (proxyConfig.auth_required) {
        return proxyConfig.username && proxyConfig.password
      }
      return true
    case 3:
      return true
    default:
      return false
  }
})

// 监听对话框显示状态
watch(() => props.visible, (val) => {
  modalVisible.value = val
  if (val) {
    loadOnlineClients()
  }
})

watch(modalVisible, (val) => {
  emit('update:visible', val)
  if (!val) {
    resetWizard()
  }
})

// 方法
const loadOnlineClients = async () => {
  loadingClients.value = true
  try {
    const response = await getClientList({ 
      page: 1, 
      page_size: 100,
      status: 1 // 只获取在线客户端
    })
    if (response.code === 200) {
      onlineClients.value = response.data.list || []
    } else {
      message.error(response.msg || '获取客户端列表失败')
    }
  } catch (error) {
    console.error('获取客户端列表失败:', error)
    message.error('获取客户端列表失败')
  } finally {
    loadingClients.value = false
  }
}

const handleClientSelect = (client) => {
  selectedClient.value = client
  selectedClientId.value = client.id
  proxyConfig.client_id = client.id
}

const handleTypeChange = () => {
  // 根据代理类型设置默认端口
  switch (proxyConfig.type) {
    case 'forward':
      proxyConfig.port = 8080
      break
    case 'reverse':
      proxyConfig.port = 80
      break
    case 'chain':
      proxyConfig.port = 8888
      break
  }
}

const getClientStatusColor = (status) => {
  return status === 1 ? '#52c41a' : '#d9d9d9'
}

const getOSColor = (os) => {
  const colors = {
    'Windows': 'blue',
    'Linux': 'green',
    'macOS': 'purple',
    'Darwin': 'purple'
  }
  return colors[os] || 'default'
}

const getProxyTypeColor = (type) => {
  const colors = {
    forward: 'green',
    reverse: 'orange',
    chain: 'blue'
  }
  return colors[type] || 'default'
}

const getProxyTypeText = (type) => {
  const texts = {
    forward: '正向代理',
    reverse: '反向代理',
    chain: '代理链'
  }
  return texts[type] || type
}

const nextStep = () => {
  if (canProceed.value && currentStep.value < 3) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleSubmit = async () => {
  try {
    submitting.value = true
    
    // 准备提交数据
    const submitData = {
      ...proxyConfig,
      // 处理IP列表
      allowed_ips: proxyConfig.allowed_ips ? proxyConfig.allowed_ips.split('\n').filter(ip => ip.trim()).join(',') : '',
      blocked_ips: proxyConfig.blocked_ips ? proxyConfig.blocked_ips.split('\n').filter(ip => ip.trim()).join(',') : '',
      // 转换速率限制单位
      rate_limit_bytes: proxyConfig.rate_limit_kbps * 1024
    }
    
    // 如果没有启用认证，清空用户名和密码
    if (!submitData.auth_required) {
      submitData.username = ''
      submitData.password = ''
    }
    
    const response = await createProxy(submitData)
    
    if (response.code === 200) {
      message.success('代理创建成功')
      emit('success')
    } else {
      message.error(response.msg || '代理创建失败')
    }
  } catch (error) {
    console.error('创建代理失败:', error)
    message.error('创建代理失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  modalVisible.value = false
}

const resetWizard = () => {
  currentStep.value = 0
  selectedClientId.value = null
  selectedClient.value = null
  
  // 重置配置
  Object.assign(proxyConfig, {
    name: '',
    description: '',
    type: 'forward',
    protocol: 'socks5',
    port: 8080,
    client_id: null,
    target_host: '',
    target_port: 80,
    backend_host: '',
    backend_port: 80,
    load_balancing: false,
    chain_strategy: 'sequential',
    health_check: false,
    auth_required: false,
    username: '',
    password: '',
    max_connections: 100,
    timeout: 30,
    buffer_size: 4096,
    log_level: 'info',
    allowed_ips: '',
    blocked_ips: '',
    rate_limit_rps: 0,
    rate_limit_kbps: 0,
    enable_metrics: true,
    enable_logging: false,
    auto_start: false
  })
}
</script>

<style scoped>
.wizard-steps {
  margin-bottom: 24px;
}

.wizard-content {
  min-height: 400px;
  padding: 24px 0;
}

.step-content h3 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.step-desc {
  margin: 0 0 24px 0;
  color: #8c8c8c;
  font-size: 14px;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.client-ip {
  color: #8c8c8c;
  font-size: 12px;
}

.client-detail {
  padding: 16px;
}

.type-specific-config {
  margin-top: 16px;
}

.wizard-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: 24px;
}

:deep(.ant-steps-item-title) {
  font-size: 14px;
}

:deep(.ant-steps-item-description) {
  font-size: 12px;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 16px 0;
}

:deep(.ant-divider-inner-text) {
  font-weight: 600;
  color: #1890ff;
}

:deep(.ant-radio-button-wrapper) {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
