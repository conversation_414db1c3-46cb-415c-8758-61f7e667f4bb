//go:build darwin
// +build darwin
package common

import (
	"bytes"
	"context"
	"encoding/binary"
	"fmt"
	"github.com/creack/pty"
	"io"
	"log"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"time"
)

func (cm *ConnectionManager) readPTYOutput(ctx context.Context) {
	type readResult struct {
		data []byte
		err  error
	}
	readChan := make(chan readResult)

	go func() {
		for {
			buf := make([]byte, 8192)
			n, err := cm.ptmx.Read(buf)
			if err != nil {
				// 如果读取出错（例如PTY关闭），发送错误并退出goroutine
				readChan <- readResult{err: err}
				return
			}
			if n > 0 {
				// 成功读取，发送数据
				readChan <- readResult{data: buf[:n]}
			}
		}
	}()

	for {
		select {
		case <-ctx.Done():
			return
		case res := <-readChan: // 如果PTY有数据或错误
			if res.err != nil {
				if res.err != io.EOF {
					log.Printf("PTY读取错误: %v", res.err)
				}
				// PTY已经关闭，我们不需要再做任何事，
				// cmd.Wait() goroutine 会捕获到进程退出并调用cancel()，
				// 这里直接返回即可。
				return
			}
			// 成功读取数据，发送到ptyOutputChan
			cm.ptyOutputChan <- res.data
		}
	}
}

func (cm *ConnectionManager) handlePTYInput(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case input := <-cm.ptyInputChan:
			_, err := cm.ptmx.Write(input)
			if err != nil {
				log.Printf("写入PTY失败: %v", err)
				return
			}
		}
	}
}

func (cm *ConnectionManager) startPTY(ctx context.Context) error {
	osName := strings.ToLower(runtime.GOOS)
	var shell string
	switch osName {
	case "windows":
		shell = "cmd.exe"
	case "linux":
		shell = "/bin/bash"
	case "darwin":
		shell = "/bin/zsh"
	default:
		shell = "/bin/sh"
	}

	cm.cmdMutex.Lock()
	defer cm.cmdMutex.Unlock()

	cmd := exec.CommandContext(ctx, shell)
	if osName != "windows" {
		cmd.Env = append(os.Environ(), "TERM=xterm-256color", "LANG=en_US.UTF-8")
	}

	// 启动 PTY
	ptmx, err := pty.StartWithSize(cmd, cm.terminalSize.size)
	if err != nil {
		return fmt.Errorf("启动PTY失败: %v", err)
	}

	cm.cmd = cmd
	cm.ptmx = ptmx

	return nil
}

func (cm *ConnectionManager) sendPTYOutput(ctx context.Context) {
	// 使用缓冲区减少小包发送
	buf := bytes.NewBuffer(nil)
	flushTimer := time.NewTicker(50 * time.Millisecond)
	defer flushTimer.Stop()

	flush := func() {
		if buf.Len() == 0 {
			return
		}

		packets, err := cm.CreatePackets(buf.Bytes(), RunCommand, ExecOutput)
		if err != nil {
			log.Printf("创建输出包失败: %v", err)
			return
		}

		for _, packet := range packets {
			packetBytes := packet.Serialize()
			if _, err = cm.conn.Write(packetBytes); err != nil {
				log.Printf("发送输出失败: %v", err)
				return
			}
		}

		buf.Reset()
	}

	for {
		select {
		case <-ctx.Done():
			flush()
			return

		case <-flushTimer.C:
			flush()

		case output, ok := <-cm.ptyOutputChan:
			if !ok {
				flush()
				return
			}

			// 添加到缓冲区
			buf.Write(output)

			// 缓冲区满时立即刷新
			if buf.Len() > 4096 {
				flush()
			}
		}
	}
}

func (cm *ConnectionManager) handleTermResize(packet *Packet) {
	if len(packet.PacketData.Data) < 4 {
		log.Printf("无效的终端大小数据")
		return
	}

	cols := binary.BigEndian.Uint16(packet.PacketData.Data[:2])
	rows := binary.BigEndian.Uint16(packet.PacketData.Data[2:4])

	size := &pty.Winsize{
		Cols: cols,
		Rows: rows,
	}

	if err := pty.Setsize(cm.ptmx, size); err != nil {
		log.Printf("调整终端大小失败: %v", err)
	} else {
		cm.terminalSize.size = size
		log.Printf("终端大小调整为: %dx%d", cols, rows)
	}
}
