<template>
  <div class="proxy-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>代理实例管理</h2>
        <p class="header-desc">管理和监控代理实例的运行状态</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon><PlusOutlined /></template>
            创建代理
          </a-button>
          <a-button @click="showConfigWizard = true">
            <template #icon><SettingOutlined /></template>
            配置向导
          </a-button>
          <a-button @click="checkProxyHealth" :loading="healthChecking">
            <template #icon><MonitorOutlined /></template>
            健康检查
          </a-button>
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="代理名称">
          <a-input
            v-model:value="searchForm.name"
            placeholder="请输入代理名称"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="代理类型">
          <a-select v-model:value="searchForm.type" placeholder="选择类型" allow-clear style="width: 150px">
            <a-select-option value="forward">正向代理</a-select-option>
            <a-select-option value="reverse">反向代理</a-select-option>
            <a-select-option value="chain">代理链</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="运行状态">
          <a-select v-model:value="searchForm.status" placeholder="选择状态" allow-clear style="width: 150px">
            <a-select-option :value="0">停止</a-select-option>
            <a-select-option :value="1">运行中</a-select-option>
            <a-select-option :value="2">错误</a-select-option>
            <a-select-option :value="3">启动中</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="客户端">
          <a-select v-model:value="searchForm.client_id" placeholder="选择客户端" allow-clear style="width: 200px">
            <a-select-option
              v-for="client in clientList"
              :key="client.id"
              :value="client.id"
            >
              {{ client.hostname }} ({{ client.client_id }})
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button @click="resetSearch">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 批量操作 -->
    <a-card v-if="selectedRowKeys.length > 0" class="batch-actions" :bordered="false">
      <a-alert
        :message="`已选择 ${selectedRowKeys.length} 个代理实例`"
        type="info"
        show-icon
        :closable="false"
      />
      <div class="batch-buttons">
        <a-space style="margin-top: 16px">
          <a-button type="primary" @click="batchStart">批量启动</a-button>
          <a-button @click="batchStop">批量停止</a-button>
          <a-button @click="batchRestart">批量重启</a-button>
          <a-button danger @click="batchDelete">批量删除</a-button>
        </a-space>
      </div>
    </a-card>

    <!-- 代理列表 -->
    <a-card class="table-card" :bordered="false">
      <a-table
        :columns="columns"
        :data-source="proxyList"
        :loading="loading"
        :row-selection="rowSelection"
        :pagination="paginationConfig"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="proxy-name">
              <a-tag :color="getProxyTypeColor(record.type)" size="small">
                {{ getProxyTypeText(record.type) }}
              </a-tag>
              <span class="name-text">{{ record.name }}</span>
            </div>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'client'">
            <div v-if="record.client_info" class="client-info">
              <MonitorOutlined />
              <span style="margin-left: 8px">{{ record.client_info.hostname }}</span>
              <a-tag size="small" color="blue" style="margin-left: 8px">{{ record.client_info.os }}</a-tag>
            </div>
            <span v-else class="text-muted">未关联</span>
          </template>

          <template v-else-if="column.key === 'connections'">
            <div class="connection-stats">
              <div>活跃: {{ record.active_connections || 0 }}</div>
              <div>总计: {{ record.total_connections || 0 }}</div>
            </div>
          </template>

          <template v-else-if="column.key === 'traffic'">
            <div class="traffic-stats">
              <div>上传: {{ formatBytes(record.bytes_transferred || 0) }}</div>
              <div>下载: {{ formatBytes(record.bytes_received || 0) }}</div>
            </div>
          </template>

          <template v-else-if="column.key === 'created_at'">
            {{ formatTime(record.created_at) }}
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button
                v-if="record.status === 0"
                type="primary"
                size="small"
                @click="startProxy(record)"
              >
                启动
              </a-button>
              <a-button
                v-else-if="record.status === 1"
                size="small"
                @click="stopProxy(record)"
              >
                停止
              </a-button>
              <a-button
                v-if="record.status === 1 || record.status === 0"
                size="small"
                @click="restartProxy(record)"
              >
                重启
              </a-button>
              <a-button type="link" size="small" @click="viewDetail(record)">
                详情
              </a-button>
              <a-popconfirm
                title="确定要删除这个代理吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteProxy(record)"
              >
                <a-button danger type="link" size="small">
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建代理模态框 -->
    <CreateProxyModal
      v-model:visible="showCreateModal"
      :client-list="clientList"
      @success="handleCreateSuccess"
    />

    <!-- 代理配置向导 -->
    <ProxyConfigWizard
      v-model:visible="showConfigWizard"
      @success="handleConfigWizardSuccess"
    />

    <!-- 代理详情模态框 -->
    <ProxyDetailModal
      v-model:visible="showDetailModal"
      :proxy-data="currentProxy"
      @success="handleUpdateSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { PlusOutlined, ReloadOutlined, MonitorOutlined, SettingOutlined } from '@ant-design/icons-vue'
import { getProxyList, controlProxy, deleteProxy as deleteProxyApi, batchProxyOperation, getProxyHealth } from '@/api/proxy'
import { getClientList } from '@/api/client'
import { formatBytes, formatTime } from '@/utils/format'
import CreateProxyModal from '@/components/proxy/CreateProxyModal.vue'
import ProxyConfigWizard from '@/components/proxy/ProxyConfigWizard.vue'
import ProxyDetailModal from '@/components/proxy/ProxyDetailModal.vue'

// 响应式数据
const loading = ref(false)
const proxyList = ref([])
const clientList = ref([])
const selectedRowKeys = ref([])
const showCreateModal = ref(false)
const showConfigWizard = ref(false)
const showDetailModal = ref(false)
const currentProxy = ref(null)
const healthChecking = ref(false)
const healthResults = ref([])

// 搜索表单
const searchForm = reactive({
  name: '',
  type: undefined,
  status: undefined,
  client_id: undefined
})

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '代理名称',
    key: 'name',
    width: 200,
    ellipsis: true
  },
  {
    title: '端口',
    dataIndex: 'port',
    key: 'port',
    width: 80
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '客户端',
    key: 'client',
    width: 200,
    ellipsis: true
  },
  {
    title: '连接统计',
    key: 'connections',
    width: 120
  },
  {
    title: '流量统计',
    key: 'traffic',
    width: 120
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    fixed: 'right'
  }
]

// 表格行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys, rows) => {
    selectedRowKeys.value = keys
  }
}

// 分页配置
const paginationConfig = computed(() => ({
  ...pagination,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    loadProxyList()
  },
  onShowSizeChange: (current, size) => {
    pagination.current = 1
    pagination.pageSize = size
    loadProxyList()
  }
}))

// 方法
const getProxyTypeColor = (type) => {
  const colors = {
    forward: 'green',
    reverse: 'orange',
    chain: 'blue'
  }
  return colors[type] || 'default'
}

const getProxyTypeText = (type) => {
  const texts = {
    forward: '正向',
    reverse: '反向',
    chain: '链式'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    0: 'default',   // 停止
    1: 'green',     // 运行中
    2: 'red',       // 错误
    3: 'orange'     // 启动中
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    0: '停止',
    1: '运行中',
    2: '错误',
    3: '启动中'
  }
  return texts[status] || '未知'
}

const loadProxyList = async () => {
  loading.value = true
  try {
    const data = {
      page: pagination.current,
      page_size: pagination.pageSize,
      ...searchForm
    }

    const response = await getProxyList(data)
    if (response.code === 200) {
      proxyList.value = response.data.list || []
      pagination.total = response.data.total || 0
    } else {
      message.error(response.msg || '获取代理列表失败')
    }
  } catch (error) {
    console.error('获取代理列表失败:', error)
    message.error('获取代理列表失败')
  } finally {
    loading.value = false
  }
}

const loadClientList = async () => {
  try {
    const response = await getClientList({ page: 1, page_size: 1000 })
    if (response.code === 200) {
      clientList.value = response.data.list || []
    }
  } catch (error) {
    console.error('获取客户端列表失败:', error)
  }
}

const refreshData = () => {
  loadProxyList()
  loadClientList()
}

const handleSearch = () => {
  pagination.current = 1
  loadProxyList()
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'status' || key === 'client_id' ? undefined : ''
  })
  handleSearch()
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadProxyList()
}

const startProxy = async (proxy) => {
  try {
    const response = await controlProxy({
      proxy_id: proxy.proxy_id,
      action: 'start'
    })
    if (response.code === 200) {
      message.success('代理启动成功')
      loadProxyList()
    } else {
      message.error(response.msg || '代理启动失败')
    }
  } catch (error) {
    console.error('代理启动失败:', error)
    message.error('代理启动失败')
  }
}

const stopProxy = async (proxy) => {
  try {
    const response = await controlProxy({
      proxy_id: proxy.proxy_id,
      action: 'stop'
    })
    if (response.code === 200) {
      message.success('代理停止成功')
      loadProxyList()
    } else {
      message.error(response.msg || '代理停止失败')
    }
  } catch (error) {
    console.error('代理停止失败:', error)
    message.error('代理停止失败')
  }
}

const restartProxy = async (proxy) => {
  try {
    const response = await controlProxy({
      proxy_id: proxy.proxy_id,
      action: 'restart'
    })
    if (response.code === 200) {
      message.success('代理重启成功')
      loadProxyList()
    } else {
      message.error(response.msg || '代理重启失败')
    }
  } catch (error) {
    console.error('代理重启失败:', error)
    message.error('代理重启失败')
  }
}

const deleteProxy = async (proxy) => {
  try {
    const response = await deleteProxyApi(proxy.id)
    if (response.code === 200) {
      message.success('代理删除成功')
      loadProxyList()
    } else {
      message.error(response.msg || '代理删除失败')
    }
  } catch (error) {
    console.error('代理删除失败:', error)
    message.error('代理删除失败')
  }
}

const viewDetail = (proxy) => {
  currentProxy.value = proxy
  showDetailModal.value = true
}

const batchStart = async () => {
  await batchOperation('start', '启动')
}

const batchStop = async () => {
  await batchOperation('stop', '停止')
}

const batchRestart = async () => {
  await batchOperation('restart', '重启')
}

const batchDelete = async () => {
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个代理吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      batchOperation('delete', '删除')
    }
  })
}

const batchOperation = async (action, actionText) => {
  try {
    // 根据选中的行键获取对应的代理ID
    const selectedProxies = proxyList.value.filter(proxy => selectedRowKeys.value.includes(proxy.id))
    const proxyIds = selectedProxies.map(proxy => proxy.proxy_id)

    const response = await batchProxyOperation({
      proxy_ids: proxyIds,
      action
    })

    if (response.code === 200) {
      const result = response.data
      if (result.success) {
        message.success(`批量${actionText}成功`)
      } else {
        message.warning(`批量${actionText}部分成功：成功 ${result.success_count} 个，失败 ${result.failed_count} 个`)
      }
      loadProxyList()
      selectedRowKeys.value = []
    } else {
      message.error(response.msg || `批量${actionText}失败`)
    }
  } catch (error) {
    console.error(`批量${actionText}失败:`, error)
    message.error(`批量${actionText}失败`)
  }
}

const handleCreateSuccess = () => {
  showCreateModal.value = false
  loadProxyList()
}

const handleConfigWizardSuccess = () => {
  showConfigWizard.value = false
  loadProxyList()
}

const handleUpdateSuccess = () => {
  showDetailModal.value = false
  loadProxyList()
}

// 健康检查
const checkProxyHealth = async () => {
  if (proxyList.value.length === 0) {
    message.warning('没有代理实例可以检查')
    return
  }

  healthChecking.value = true
  try {
    const proxyIds = proxyList.value.map(proxy => proxy.proxy_id).join(',')
    const response = await getProxyHealth({
      proxy_ids: proxyIds,
      force: true
    })

    if (response.code === 200) {
      healthResults.value = response.data || []

      // 显示健康检查结果摘要
      const healthyCount = healthResults.value.filter(r => r.overall_health === 'healthy').length
      const warningCount = healthResults.value.filter(r => r.overall_health === 'warning').length
      const criticalCount = healthResults.value.filter(r => r.overall_health === 'critical').length

      let messageText = `健康检查完成: ${healthyCount} 个健康`
      if (warningCount > 0) messageText += `, ${warningCount} 个警告`
      if (criticalCount > 0) messageText += `, ${criticalCount} 个严重`

      if (criticalCount > 0) {
        message.error(messageText)
      } else if (warningCount > 0) {
        message.warning(messageText)
      } else {
        message.success(messageText)
      }

      // 刷新代理列表以更新状态
      loadProxyList()
    } else {
      message.error(response.msg || '健康检查失败')
    }
  } catch (error) {
    console.error('健康检查失败:', error)
    message.error('健康检查失败')
  } finally {
    healthChecking.value = false
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.proxy-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.search-card {
  margin-bottom: 16px;
}

.batch-actions {
  margin-bottom: 16px;
}

.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.proxy-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name-text {
  font-weight: 500;
  margin-left: 8px;
}

.client-info {
  display: flex;
  align-items: center;
}

.connection-stats,
.traffic-stats {
  font-size: 12px;
  line-height: 1.4;
  color: #595959;
}

.text-muted {
  color: #8c8c8c;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

:deep(.ant-tag) {
  border-radius: 4px;
}
</style>
