<template>
  <a-modal
    v-model:visible="modalVisible"
    title="添加代理节点"
    width="600px"
    :confirm-loading="submitting"
    @ok="handleSubmit"
    @cancel="handleClose"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="节点名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入节点名称" />
      </a-form-item>

      <a-form-item label="节点类型" name="type">
        <a-radio-group v-model:value="form.type">
          <a-radio value="forward">正向代理节点</a-radio>
          <a-radio value="reverse">反向代理节点</a-radio>
        </a-radio-group>
        <div style="font-size: 12px; color: #999; margin-top: 4px;">
          正向代理：Client等待用户连接；反向代理：Client主动连接Server
        </div>
      </a-form-item>

      <a-form-item label="关联客户端" name="client_id">
        <a-select v-model:value="form.client_id" placeholder="选择客户端">
          <a-select-option
            v-for="client in clientList"
            :key="client.id"
            :value="client.id"
          >
            {{ client.hostname }} ({{ client.client_id }})
          </a-select-option>
        </a-select>
        <div style="font-size: 12px; color: #999; margin-top: 4px;">
          选择要使用的客户端作为代理链节点
        </div>
      </a-form-item>

      <a-form-item label="节点优先级">
        <a-input-number
          v-model:value="form.priority"
          :min="1"
          :max="100"
          style="width: 100%"
          placeholder="数值越小优先级越高"
        />
        <div style="font-size: 12px; color: #999; margin-top: 4px;">
          节点在代理链中的优先级，系统将按优先级顺序构建链路
        </div>
      </a-form-item>

      <a-divider orientation="left">高级设置</a-divider>

      <a-form-item label="协议类型">
        <a-select v-model:value="form.protocol" disabled>
          <a-select-option value="socks5">SOCKS5 (iox标准)</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="认证信息">
        <a-checkbox v-model:checked="form.auth_required">需要认证</a-checkbox>
      </a-form-item>

      <div v-if="form.auth_required">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户名" name="username">
              <a-input v-model:value="form.username" placeholder="请输入用户名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="密码" name="password">
              <a-input-password v-model:value="form.password" placeholder="请输入密码" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="权重">
            <a-input-number
              v-model:value="form.weight"
              :min="1"
              :max="100"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="优先级">
            <a-input-number
              v-model:value="form.priority"
              :min="1"
              :max="10"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="高级选项">
        <a-space direction="vertical">
          <a-checkbox v-model:checked="form.enable_health_check">启用健康检查</a-checkbox>
          <a-checkbox v-model:checked="form.enable_failover">启用故障转移</a-checkbox>
          <a-checkbox v-model:checked="form.enable_metrics">启用指标收集</a-checkbox>
        </a-space>
      </a-form-item>

      <div v-if="form.enable_health_check">
        <a-divider orientation="left">健康检查配置</a-divider>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="检查间隔(秒)">
              <a-input-number
                v-model:value="form.health_check_interval"
                :min="5"
                :max="300"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="超时时间(秒)">
              <a-input-number
                v-model:value="form.health_check_timeout"
                :min="1"
                :max="60"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <a-form-item label="描述">
        <a-textarea
          v-model:value="form.description"
          :rows="2"
          placeholder="请输入节点描述（可选）"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { addProxyChainNode } from '@/api/proxy'
import { getClientList } from '@/api/client'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  chainId: {
    type: String,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const modalVisible = ref(false)
const clientList = ref([]) // 客户端列表

// 表单数据（iox代理链标准）
const form = reactive({
  name: '',
  type: 'forward', // forward 或 reverse
  client_id: null, // 关联的客户端ID
  protocol: 'socks5', // 固定为socks5（iox标准）
  priority: 1, // 节点优先级
  description: ''
})

// 表单验证规则（iox代理链标准）
const rules = {
  name: [
    { required: true, message: '请输入节点名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择节点类型', trigger: 'change' }
  ],
  client_id: [
    { required: true, message: '请选择关联客户端', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请输入节点优先级', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '优先级必须在 1-100 之间', trigger: 'blur' }
  ]
}

// 监听对话框显示状态
watch(() => props.visible, (val) => {
  modalVisible.value = val
})

watch(modalVisible, (val) => {
  emit('update:visible', val)
  if (val) {
    loadClientList() // 打开对话框时加载客户端列表
  }
})

// 加载客户端列表
const loadClientList = async () => {
  try {
    const response = await getClientList({ status: 1 }) // 只获取在线客户端
    clientList.value = response.data || []
  } catch (error) {
    console.error('获取客户端列表失败:', error)
    message.error('获取客户端列表失败')
  }
}

// 方法
const handleClose = () => {
  modalVisible.value = false
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    name: '',
    type: 'forward', // forward 或 reverse
    client_id: null, // 关联的客户端ID
    protocol: 'socks5', // 固定为socks5（iox标准）
    priority: 1, // 节点优先级
    description: ''
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const submitData = {
      chain_id: props.chainId,
      name: form.name,
      type: form.type, // forward 或 reverse
      client_id: form.client_id,
      priority: form.priority,
      description: form.description
    }
    
    const response = await addProxyChainNode(submitData)
    
    if (response.code === 200) {
      message.success('节点添加成功')
      emit('success')
    } else {
      message.error(response.msg || '节点添加失败')
    }
  } catch (error) {
    if (error.errorFields) {
      return
    }
    console.error('添加节点失败:', error)
    message.error('添加节点失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 16px 0;
}

:deep(.ant-divider-inner-text) {
  font-weight: 600;
  color: #1890ff;
}
</style>
