package proxy

import "server/model/request"

// ProxyCreateRequest 创建代理实例请求
type ProxyCreateRequest struct {
	Name           string `json:"name" binding:"required" validate:"min=1,max=100"`
	Description    string `json:"description" validate:"max=500"`
	Type           string `json:"type" binding:"required" validate:"oneof=forward reverse"`
	UserPort       uint16 `json:"user_port" validate:"min=0,max=65535"` // 0表示自动分配
	AuthRequired   bool   `json:"auth_required"`
	AllocMode      string `json:"alloc_mode"`
	Username       string `json:"username" validate:"max=50"`
	Password       string `json:"password" validate:"max=100"`
	MaxConnections int    `json:"max_connections" validate:"min=1,max=10000"`
	Timeout        int    `json:"timeout" validate:"min=1,max=300"`
	ClientID       uint   `json:"client_id" binding:"required"`
	ListenerID     uint   `json:"listener_id"`
	AllowedIPs     string `json:"allowed_ips"`
	BlockedIPs     string `json:"blocked_ips"`
}

type ProxyControlRequest struct {
	TaskID  uint64 `json:"task_id"`
	ProxyID string `json:"proxy_id" binding:"required"`
	Action  string `json:"action" binding:"required" validate:"oneof=start stop restart"`
}

// ProxyStartRequest 启动代理请求
type ProxyStartRequest struct {
	TaskID      uint64 `json:"task_id"`
	ProxyID     string `json:"proxy_id"`
	AllocMode   string `json:"alloc_mode"`
	ManualAlloc bool   `json:"manual_alloc"`
	Type        string `json:"type" binding:"required" validate:"oneof=forward reverse"`
	UserPort    uint16 `json:"user_port"` // User端口
	ClientPort  uint16 `json:"client_port"`
	Name        string `json:"name"` // 代理名称
}

// ProxyStopRequest 停止代理请求
type ProxyStopRequest struct {
	TaskID  uint64 `json:"task_id"`
	ProxyID string `json:"proxy_id"` // 使用uint，对应数据库ID
}

// ProxyDeleteRequest 删除代理请求，删除+停止代理
type ProxyDeleteRequest struct {
	TaskID   uint64 `json:"task_id"`
	ClientID uint   `json:"client_id"`
	ProxyID  string `json:"proxy_id"` // 使用uint，对应数据库ID
}

type ProxyUpdateRequest struct {
	ID           uint   `json:"id" binding:"required"`
	Name         string `json:"name" validate:"min=1,max=100"`
	Description  string `json:"description" validate:"max=500"`
	AuthRequired bool   `json:"auth_required"`
	Username     string `json:"username" validate:"max=50"`
	Password     string `json:"password" validate:"max=100"`
	AllowedIPs   string `json:"allowed_ips"`
	BlockedIPs   string `json:"blocked_ips"`
}

// ProxyStatusRequest 获取代理状态请求
type ProxyStatusRequest struct {
	TaskID  uint64 `json:"task_id"`
	ProxyID string `json:"proxy_id"`
}

// ProxyListRequest 获取代理列表请求
type ProxyListRequest struct {
	TaskID uint64 `json:"task_id"`
	request.PageInfo
	ProxyID  string `json:"proxy_id,omitempty"`
	Name     string `json:"name,omitempty"`
	Type     string `json:"type" binding:"required" validate:"oneof=forward reverse"`
	Status   *int   `json:"status,omitempty"`
	ClientID *uint  `json:"client_id,omitempty"`
}

type CheckPortRequest struct {
	TaskID   uint64 `json:"task_id"`
	Port     uint16 `json:"port"`
	ClientID uint   `json:"client_id"`
	Type     string `json:"type"` // forward, reverse
}
