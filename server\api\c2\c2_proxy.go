package c2

import (
	"server/core/manager"
	"server/global"
	"server/model/request/proxy"
	"server/model/response"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProxyApi struct{}

// CreateProxy 创建代理实例
func (p *ProxyApi) CreateProxy(ctx *gin.Context) {
	var req proxy.ProxyCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	// 参数验证
	if req.Name == "" {
		response.ErrorWithMessage("代理名称不能为空", ctx)
		return
	}
	if req.Type == "" {
		response.ErrorWithMessage("代理类型不能为空", ctx)
		return
	}
	// 端口验证：0表示自动分配，其他值必须在有效范围内
	if req.UserPort < 0 || req.UserPort > 65535 {
		response.ErrorWithMessage("端口号必须在0-65535之间，0表示自动分配", ctx)
		return
	}
	if req.ClientID == 0 {
		response.ErrorWithMessage("客户端ID不能为空", ctx)
		return
	}

	proxy, err := proxyService.CreateProxy(req)
	if err != nil {
		global.LOG.Error("创建代理实例失败", zap.Error(err))
		response.ErrorWithMessage("创建代理实例失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(proxy, ctx)
}

// UpdateProxyI 更新代理实例
func (p *ProxyApi) UpdateProxy(ctx *gin.Context) {
	var req proxy.ProxyUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	if req.ID == 0 {
		response.ErrorWithMessage("代理实例ID不能为空", ctx)
		return
	}

	proxy, err := proxyService.UpdateProxy(req)
	if err != nil {
		global.LOG.Error("更新代理实例失败", zap.Error(err))
		response.ErrorWithMessage("更新代理实例失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(proxy, ctx)
}

// GetProxyInstance 获取代理实例详情
func (p *ProxyApi) GetProxyInstance(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("无效的代理实例ID", ctx)
		return
	}

	proxy, err := proxyService.GetProxyInstance(uint(id))
	if err != nil {
		global.LOG.Error("获取代理实例失败", zap.Error(err))
		response.ErrorWithMessage("获取代理实例失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(proxy, ctx)
}

// ControlProxyInstance 控制代理实例（启动/停止/重启）
func (p *ProxyApi) ControlProxy(ctx *gin.Context) {
	var req proxy.ProxyControlRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	if req.ProxyID == "" {
		response.ErrorWithMessage("代理ID不能为空", ctx)
		return
	}
	if req.Action == "" {
		response.ErrorWithMessage("操作类型不能为空", ctx)
		return
	}

	// 验证操作类型
	validActions := map[string]bool{
		"start":   true,
		"stop":    true,
		"restart": true,
	}
	if !validActions[req.Action] {
		response.ErrorWithMessage("无效的操作类型", ctx)
		return
	}

	taskID, err := proxyService.ControlProxy(req)
	if err != nil {
		global.LOG.Error("控制代理实例失败", zap.Error(err))
		response.ErrorWithMessage("控制代理实例失败: "+err.Error(), ctx)
		return
	}
	waitForResponseAsync(ctx, taskID, "控制代理实例")
}

func (p *ProxyApi) CheckServerPortAvailability(ctx *gin.Context) {
	var req struct {
		Port uint16 `json:"port" binding:"required,min=1024,max=65535"`
		Type string `json:"type" binding:"required,oneof=forward reverse chain"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}
	available, reason := manager.CheckPortAvailable(req.Port)
	inRecommendedRange := req.Port >= 20000

	result := map[string]interface{}{
		"available":            available,
		"port":                 req.Port,
		"type":                 req.Type,
		"reason":               reason,
		"in_recommended_range": inRecommendedRange,
		"recommended_range":    "20000-65535",
	}
	switch req.Type {
	case "forward":
		result["description"] = "正向代理：Server端开启SOCKS5服务，Client连接此端口"
		result["location"] = "Server端"
	case "reverse":
		result["description"] = "反向代理：Server端开启用户SOCKS5服务"
		result["location"] = "Server端"
	case "chain":
		result["description"] = "代理链：Server端开启SOCKS5服务，用户连接使用代理链"
		result["location"] = "Server端"
	}

	if !inRecommendedRange {
		result["warning"] = "建议使用28000-48000端口范围以符合iox代理系统标准"
	}

	response.OkWithData(result, ctx)
}

func (p *ProxyApi) CheckClientPortAvailability(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req proxy.CheckPortRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}
	taskID, err := proxyService.CheckClientPortAvailability(uint(clientID), req)
	if err != nil {
		global.LOG.Error("测试客户端端口可用性失败", zap.Error(err))
		response.ErrorWithMessage("测试客户端端口可用性失败: "+err.Error(), ctx)
		return
	}
	waitForResponseAsync(ctx, taskID, "测试客户端端口可用性")
}


func (p *ProxyApi) ListProxy(ctx *gin.Context) {
	var req proxy.ProxyListRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	taskID, err := proxyService.ListProxy(req)
	if err != nil {
		global.LOG.Error("列出代理失败", zap.Error(err))
		response.ErrorWithMessage("列出代理失败: "+err.Error(), ctx)
		return
	}
	waitForResponseAsync(ctx, taskID, "列出代理")
}

func (p *ProxyApi) DeleteProxy(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("无效的代理实例ID", ctx)
		return
	}

	taskID, err := proxyService.DeleteProxy(uint(id))
	if err != nil {
		global.LOG.Error("删除代理失败", zap.Error(err))
		response.ErrorWithMessage("删除代理失败: "+err.Error(), ctx)
		return
	}
	waitForResponseAsync(ctx, taskID, "删除代理")
}
