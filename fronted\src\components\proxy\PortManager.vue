<template>
  <div class="port-manager">
    <!-- 端口配置模式选择 -->
    <a-form-item :label="label">
      <div class="port-mode-selector">
        <a-radio-group v-model:value="portMode" @change="onModeChange">
          <a-radio-button value="auto">自动分配</a-radio-button>
          <a-radio-button value="manual">手动设置</a-radio-button>
        </a-radio-group>
        
        <!-- 配置说明按钮 -->
        <a-button
          type="link"
          size="small"
          @click="showConfigGuide"
          style="margin-left: 8px;"
        >
          ❓ 配置说明
        </a-button>
      </div>
    </a-form-item>

    <!-- 自动分配模式 -->
    <template v-if="portMode === 'auto'">
      <a-form-item label="端口分配">
        <a-input 
          :value="autoPortDisplay" 
          disabled 
          style="background-color: #f5f5f5;"
        />
        <div class="port-info">
          <a-typography-text type="secondary" style="font-size: 12px;">
            {{ portDescription }}
          </a-typography-text>
        </div>
      </a-form-item>
    </template>

    <!-- 手动设置模式 -->
    <template v-if="portMode === 'manual'">
      <a-form-item 
        :label="manualPortLabel" 
        :name="portFieldName"
        :rules="portValidationRules"
      >
        <a-input-group compact>
          <a-input-number
            v-model:value="manualPort"
            :min="1024"
            :max="65535"
            :placeholder="portPlaceholder"
            style="width: 60%;"
            @change="onPortChange"
          />
          <a-button
            type="primary"
            :loading="testing"
            @click="testPort"
            style="width: 25%;"
          >
            {{ testing ? '⏳ 测试中' : '✅ 测试端口' }}
          </a-button>
          <a-button
            type="default"
            @click="showPortInfo"
            style="width: 15%;"
          >
            ℹ️
          </a-button>
        </a-input-group>
        
        <!-- 端口测试结果 -->
        <div v-if="testResult" class="port-test-result" style="margin-top: 8px;">
          <a-alert 
            :message="testResult.message"
            :type="testResult.type"
            :show-icon="true"
            closable
            @close="clearTestResult"
          />
        </div>
        
        <!-- 端口说明 -->
        <div class="port-info" style="margin-top: 4px;">
          <a-typography-text type="secondary" style="font-size: 12px;">
            {{ manualPortDescription }}
          </a-typography-text>
        </div>
      </a-form-item>
    </template>

    <!-- 端口信息弹窗 -->
    <a-modal
      v-model:visible="portInfoVisible"
      title="端口配置详情"
      :footer="null"
      width="600px"
    >
      <div class="port-info-content">
        <a-descriptions :column="1" bordered size="small">
          <a-descriptions-item label="代理类型">
            <a-tag :color="proxyTypeColor">{{ proxyTypeText }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="端口用途">
            {{ portUsageDescription }}
          </a-descriptions-item>
          <a-descriptions-item label="开启位置">
            <a-tag :color="portLocationColor">{{ portLocationText }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="端口范围">
            {{ portRangeText }}
          </a-descriptions-item>
          <a-descriptions-item label="协议类型">
            <a-tag color="blue">{{ protocolText }}</a-tag>
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <div class="port-architecture">
          <h4>端口架构说明</h4>
          <div class="architecture-diagram">
            {{ architectureDescription }}
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 代理配置工具书弹窗 -->
    <ProxyConfigGuide
      v-model="configGuideVisible"
      :default-tab="props.proxyType"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
// 使用字符串图标名称，避免依赖问题
// import {
//   QuestionCircleOutlined,
//   CheckCircleOutlined,
//   LoadingOutlined,
//   InfoCircleOutlined
// } from '@ant-design-vue/icons-vue'
import { checkServerPortAvailability } from '@/api/proxy'
import ProxyConfigGuide from './ProxyConfigGuide.vue'

// Props
const props = defineProps({
  proxyType: {
    type: String,
    required: true,
    validator: value => ['forward', 'reverse', 'chain'].includes(value)
  },
  label: {
    type: String,
    default: '端口配置'
  },
  modelValue: {
    type: [Number, Object],
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const portMode = ref('auto') // auto | manual
const manualPort = ref(null)
const testing = ref(false)
const testResult = ref(null)
const portInfoVisible = ref(false)
const configGuideVisible = ref(false)

// 计算属性
const autoPortDisplay = computed(() => {
  switch (props.proxyType) {
    case 'forward':
      return '系统自动分配 (28000-48000)'
    case 'reverse':
      return 'Client端口: 自动分配 | 用户SOCKS5端口: 自动分配'
    case 'chain':
      return '用户SOCKS5端口: 系统自动分配 (28000-48000)'
    default:
      return '系统自动分配'
  }
})

const portDescription = computed(() => {
  switch (props.proxyType) {
    case 'forward':
      return '🔄 Server端开启SOCKS5服务，Client连接此端口提供代理服务'
    case 'reverse':
      return '🔄 Server端双端口：Client连接端口 + 用户SOCKS5端口'
    case 'chain':
      return '🔄 Server端开启SOCKS5服务，用户连接此端口使用代理链'
    default:
      return ''
  }
})

const manualPortLabel = computed(() => {
  switch (props.proxyType) {
    case 'forward':
      return 'SOCKS5端口'
    case 'reverse':
      return '用户SOCKS5端口'
    case 'chain':
      return '用户SOCKS5端口'
    default:
      return '端口'
  }
})

const portPlaceholder = computed(() => {
  return `请输入${manualPortLabel.value} (1024-65535)`
})

const manualPortDescription = computed(() => {
  switch (props.proxyType) {
    case 'forward':
      return '🖥️ Server端口：用户连接此端口使用正向代理服务'
    case 'reverse':
      return '🖥️ Server端口：用户连接此端口，流量通过Client转发'
    case 'chain':
      return '🖥️ Server端口：用户连接此端口使用代理链服务'
    default:
      return ''
  }
})

const proxyTypeText = computed(() => {
  const typeMap = {
    forward: '正向代理 (ForwardProxyIOX)',
    reverse: '反向代理 (ReverseProxyIOX)', 
    chain: '代理链 (ProxyChainIOX)'
  }
  return typeMap[props.proxyType] || props.proxyType
})

const proxyTypeColor = computed(() => {
  const colorMap = {
    forward: 'green',
    reverse: 'blue',
    chain: 'purple'
  }
  return colorMap[props.proxyType] || 'default'
})

const portUsageDescription = computed(() => {
  switch (props.proxyType) {
    case 'forward':
      return 'Client启动SOCKS5服务器，Server连接Client，用户连接Server端口使用代理'
    case 'reverse':
      return 'Server启动双端口服务，Client主动连接，用户连接SOCKS5端口使用代理'
    case 'chain':
      return 'Server启动SOCKS5服务，用户连接后流量经过代理链中的多个节点'
    default:
      return ''
  }
})

const portLocationText = computed(() => {
  switch (props.proxyType) {
    case 'forward':
      return 'Server端开启'
    case 'reverse':
      return 'Server端开启'
    case 'chain':
      return 'Server端开启'
    default:
      return ''
  }
})

const portLocationColor = computed(() => {
  return 'orange'
})

const portRangeText = computed(() => {
  return '28000-48000 (iox代理系统标准端口范围)'
})

const protocolText = computed(() => {
  return 'SOCKS5'
})

const architectureDescription = computed(() => {
  switch (props.proxyType) {
    case 'forward':
      return `
正向代理架构：
用户 → Server(SOCKS5端口) → Client(SOCKS5服务器) → 目标服务器

1. Client启动SOCKS5服务器
2. Server连接Client的SOCKS5服务器
3. 用户连接Server的SOCKS5端口
4. 流量通过Client转发到目标服务器
      `
    case 'reverse':
      return `
反向代理架构：
用户 → Server(用户SOCKS5端口) ← Client(主动连接Client端口)

1. Server启动双端口服务
2. Client主动连接Server的Client端口
3. 用户连接Server的用户SOCKS5端口
4. 流量通过Client转发
      `
    case 'chain':
      return `
代理链架构：
用户 → Server(SOCKS5端口) → 节点1 → 节点2 → ... → 目标服务器

1. Server启动SOCKS5服务
2. 用户连接Server的SOCKS5端口
3. 流量按照代理链顺序经过多个节点
4. 支持正向代理和反向代理混合
      `
    default:
      return ''
  }
})

const portFieldName = computed(() => {
  return `${props.proxyType}_port`
})

const portValidationRules = computed(() => {
  return [
    { required: true, message: `请输入${manualPortLabel.value}`, trigger: 'blur' },
    { type: 'number', min: 1024, max: 65535, message: '端口号必须在 1024-65535 之间', trigger: 'blur' }
  ]
})

// 方法
const onModeChange = () => {
  clearTestResult()
  updateModelValue()
}

const onPortChange = () => {
  clearTestResult()
  updateModelValue()
}

const updateModelValue = () => {
  if (portMode.value === 'auto') {
    emit('update:modelValue', { mode: 'auto', port: null })
  } else {
    emit('update:modelValue', { mode: 'manual', port: manualPort.value })
  }
}

const testPort = async () => {
  if (!manualPort.value) {
    message.warning('请先输入端口号')
    return
  }

  testing.value = true
  clearTestResult()

  try {
      const response = await checkServerPortAvailability({
      port: manualPort.value,
      type: props.proxyType
    })

    if (response.data.available) {
      testResult.value = {
        type: 'success',
        message: `端口 ${manualPort.value} 可用，可以正常使用`
      }
    } else {
      testResult.value = {
        type: 'error',
        message: `端口 ${manualPort.value} 不可用：${response.data.reason || '端口被占用'}`
      }
    }
  } catch (error) {
    testResult.value = {
      type: 'error',
      message: `端口测试失败：${error.message || '网络错误'}`
    }
  } finally {
    testing.value = false
  }
}

const clearTestResult = () => {
  testResult.value = null
}

const showPortInfo = () => {
  portInfoVisible.value = true
}

const showConfigGuide = () => {
  configGuideVisible.value = true
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue && typeof newValue === 'object') {
    portMode.value = newValue.mode || 'auto'
    manualPort.value = newValue.port || null
  }
}, { immediate: true })

// 初始化
updateModelValue()
</script>

<style scoped>
.port-manager {
  width: 100%;
}

.port-mode-selector {
  display: flex;
  align-items: center;
}

.port-info {
  margin-top: 4px;
}

.port-test-result {
  animation: fadeIn 0.3s ease-in;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #fafafa;
}

.architecture-diagram {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-line;
  font-size: 12px;
  line-height: 1.4;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}
</style>
