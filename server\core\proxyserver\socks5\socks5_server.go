// socks5_server.go
package socks5

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net"
	"server/core/dbpool"
	"server/global"
	"server/model/basic"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

/*
反向代理的实现

User (外网)
 |
 v
(userPort)
Server (clientPortA) <===>  Client (内网)

userPort面向用户: 可以由用户自定义，也可以自动分配
clientPort面向Client: 纯自动分配
*/

// ===== 模块化组件 =====

// ProxyConfig 代理配置
type ProxyConfig struct {
	*basic.Proxy
	allowedIPs []string
	blockedIPs []string
}

// NewProxyConfig 创建代理配置
func NewProxyConfig(proxy *basic.Proxy) *ProxyConfig {
	config := &ProxyConfig{Proxy: proxy}

	// 解析IP列表
	if proxy.AllowedIPs != "" {
		config.allowedIPs = parseIPList(proxy.AllowedIPs)
	}
	if proxy.BlockedIPs != "" {
		config.blockedIPs = parseIPList(proxy.BlockedIPs)
	}

	return config
}

// parseIPList 解析逗号分隔的IP列表
func parseIPList(ipStr string) []string {
	if ipStr == "" {
		return nil
	}
	ips := strings.Split(ipStr, ",")
	result := make([]string, 0, len(ips))
	for _, ip := range ips {
		ip = strings.TrimSpace(ip)
		if ip != "" {
			result = append(result, ip)
		}
	}
	return result
}

// AccessController 访问控制器
type AccessController struct {
	config *ProxyConfig
}

// NewAccessController 创建访问控制器
func NewAccessController(config *ProxyConfig) *AccessController {
	return &AccessController{config: config}
}

// IsAllowed 检查IP是否被允许访问
func (ac *AccessController) IsAllowed(clientIP string) bool {
	// 首先检查黑名单
	for _, blockedIP := range ac.config.blockedIPs {
		if matchIP(clientIP, blockedIP) {
			return false
		}
	}

	// 如果没有白名单，则允许（除非在黑名单中）
	if len(ac.config.allowedIPs) == 0 {
		return true
	}

	// 检查白名单
	for _, allowedIP := range ac.config.allowedIPs {
		if matchIP(clientIP, allowedIP) {
			return true
		}
	}

	return false
}

// matchIP 简单的IP匹配（支持通配符*）
func matchIP(clientIP, pattern string) bool {
	if pattern == "*" {
		return true
	}
	if pattern == clientIP {
		return true
	}
	// 简单的前缀匹配，如 192.168.1.*
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(clientIP, prefix)
	}
	return false
}

// ProxyStats 代理统计信息
type ProxyStats struct {
	totalConnections  int64
	activeConnections int64
	bytesTransferred  int64
	bytesReceived     int64
	lastActivity      time.Time
	mutex             sync.RWMutex

	// 定期更新相关
	updateTicker *time.Ticker
	updateStop   chan struct{}
	proxy        *basic.Proxy
}

// NewProxyStats 创建统计对象
func NewProxyStats(proxy *basic.Proxy) *ProxyStats {
	stats := &ProxyStats{
		lastActivity: time.Now(),
		updateStop:   make(chan struct{}),
		proxy:        proxy,
	}

	// 启动定期更新
	stats.startPeriodicUpdate()

	return stats
}

// AddConnection 增加连接计数
func (ps *ProxyStats) AddConnection() {
	atomic.AddInt64(&ps.totalConnections, 1)
	atomic.AddInt64(&ps.activeConnections, 1)
	ps.updateLastActivity()
}

// RemoveConnection 减少连接计数
func (ps *ProxyStats) RemoveConnection() {
	atomic.AddInt64(&ps.activeConnections, -1)
}

// AddBytes 增加传输字节数
func (ps *ProxyStats) AddBytes(sent, received int64) {
	atomic.AddInt64(&ps.bytesTransferred, sent)
	atomic.AddInt64(&ps.bytesReceived, received)
	ps.updateLastActivity()
}

// updateLastActivity 更新最后活动时间
func (ps *ProxyStats) updateLastActivity() {
	ps.mutex.Lock()
	ps.lastActivity = time.Now()
	ps.mutex.Unlock()
}

// GetStats 获取统计信息
func (ps *ProxyStats) GetStats() (int64, int64, int64, int64, time.Time) {
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	return atomic.LoadInt64(&ps.totalConnections),
		atomic.LoadInt64(&ps.activeConnections),
		atomic.LoadInt64(&ps.bytesTransferred),
		atomic.LoadInt64(&ps.bytesReceived),
		ps.lastActivity
}

// startPeriodicUpdate 启动定期更新统计信息到数据库
func (ps *ProxyStats) startPeriodicUpdate() {
	ps.updateTicker = time.NewTicker(10 * time.Second) // 每10秒更新一次
	go func() {
		for {
			select {
			case <-ps.updateTicker.C:
				ps.updateToDatabase()
			case <-ps.updateStop:
				ps.updateTicker.Stop()
				return
			}
		}
	}()
}

// stopPeriodicUpdate 停止定期更新
func (ps *ProxyStats) stopPeriodicUpdate() {
	if ps.updateStop != nil {
		close(ps.updateStop)
	}
}

// updateToDatabase 更新统计信息到数据库
func (ps *ProxyStats) updateToDatabase() {
	if ps.proxy == nil {
		return
	}

	totalConn, activeConn, bytesSent, bytesReceived, lastActivity := ps.GetStats()

	// 直接更新数据库中的统计字段
	updates := map[string]interface{}{
		"total_connections":  totalConn,
		"active_connections": int(activeConn),
		"bytes_transferred":  bytesSent,
		"bytes_received":     bytesReceived,
		"last_activity":      lastActivity,
		"version":            ps.proxy.Version + 1,
	}

	// 🚀 使用数据库连接池异步更新代理统计信息
	if err := dbpool.ExecuteDBOperationAsyncAndWait("socks5_proxy_stats_update", func(db *gorm.DB) error {
		return db.Model(ps.proxy).Updates(updates).Error
	}); err != nil {
		global.LOG.Error("更新代理统计信息失败",
			zap.String("proxyID", ps.proxy.ProxyID),
			zap.Error(err))
	} else {
		// 更新内存中的版本号
		ps.proxy.Version++
		ps.proxy.TotalConnections = totalConn
		ps.proxy.ActiveConnections = int(activeConn)
		ps.proxy.BytesTransferred = bytesSent
		ps.proxy.BytesReceived = bytesReceived
		ps.proxy.LastActivity = lastActivity
	}
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	connections map[string]net.Conn
	mutex       sync.RWMutex
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager() *ConnectionManager {
	return &ConnectionManager{
		connections: make(map[string]net.Conn),
	}
}

// AddConnection 添加连接
func (cm *ConnectionManager) AddConnection(id string, conn net.Conn) {
	cm.mutex.Lock()
	cm.connections[id] = conn
	cm.mutex.Unlock()
}

// RemoveConnection 移除连接
func (cm *ConnectionManager) RemoveConnection(id string) {
	cm.mutex.Lock()
	if conn, exists := cm.connections[id]; exists {
		conn.Close()
		delete(cm.connections, id)
	}
	cm.mutex.Unlock()
}

// CloseAll 关闭所有连接
func (cm *ConnectionManager) CloseAll() {
	cm.mutex.Lock()
	for id, conn := range cm.connections {
		conn.Close()
		delete(cm.connections, id)
	}
	cm.mutex.Unlock()
}

// ===== 主要结构体 =====

type Socks5ReverseProxyServer struct {
	// 基本配置
	userPort       uint16
	clientPort     uint16
	clientConn     net.Conn
	userListener   net.Listener
	clientListener net.Listener

	// 模块化组件
	config            *ProxyConfig
	accessController  *AccessController
	stats             *ProxyStats
	connectionManager *ConnectionManager

	// 控制
	lock    sync.RWMutex
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
}

type Socks5Manager struct {
	servers map[*basic.Proxy]*Socks5ReverseProxyServer
	lock    sync.Mutex
}

var GlobalSocks5Manager = &Socks5Manager{
	servers: make(map[*basic.Proxy]*Socks5ReverseProxyServer),
}

// StartServer 在指定端口启动 Socks5 服务
func (m *Socks5Manager) StartServer(proxy *basic.Proxy) error {
	m.lock.Lock()
	defer m.lock.Unlock()

	if _, exists := m.servers[proxy]; exists {
		return errors.New("反向Socks5服务器已存在")
	}

	ctx, cancel := context.WithCancel(context.Background())

	// 创建模块化组件
	config := NewProxyConfig(proxy)
	accessController := NewAccessController(config)
	stats := NewProxyStats(proxy)
	connectionManager := NewConnectionManager()

	server := &Socks5ReverseProxyServer{
		userPort:          proxy.UserPort,
		clientPort:        proxy.ClientPort,
		config:            config,
		accessController:  accessController,
		stats:             stats,
		connectionManager: connectionManager,
		ctx:               ctx,
		cancel:            cancel,
		running:           false,
	}

	if err := server.Start(proxy); err != nil {
		cancel()
		return err
	}

	// 启动成功后添加到map中
	m.servers[proxy] = server
	return nil
}

// Start 启动服务
func (s *Socks5ReverseProxyServer) Start(proxy *basic.Proxy) error {
	s.lock.Lock()
	if s.running {
		s.lock.Unlock()
		return errors.New("服务已在运行")
	}
	s.running = true
	s.lock.Unlock()

	// 启动 Client 监听（PortA）
	clientAddr := fmt.Sprintf(":%d", s.clientPort)
	clientListener, err := net.Listen("tcp", clientAddr)
	if err != nil {
		s.lock.Lock()
		s.running = false
		s.lock.Unlock()
		return fmt.Errorf("启动Client监听器失败: %v", err)
	}
	s.clientListener = clientListener

	// 启动 User 监听（PortB）
	userAddr := fmt.Sprintf(":%d", s.userPort)
	userListener, err := net.Listen("tcp", userAddr)
	if err != nil {
		clientListener.Close()
		s.lock.Lock()
		s.running = false
		s.lock.Unlock()
		return fmt.Errorf("启动User监听器失败: %v", err)
	}
	s.userListener = userListener

	// 更新代理状态和启动时间
	s.config.Status = 1 // 运行中
	s.config.StartedAt = time.Now()
	s.config.ErrorCount = 0
	s.config.LastError = ""

	// 注意：这里不需要手动保存，因为统计信息会通过定期更新自动保存

	global.LOG.Info("反向代理启动成功",
		zap.Uint16("userPort", s.userPort),
		zap.Uint16("clientPort", s.clientPort),
		zap.Bool("authRequired", s.config.AuthRequired),
		zap.Int("allowedIPs", len(s.config.allowedIPs)),
		zap.Int("blockedIPs", len(s.config.blockedIPs)))

	// 启动Client连接处理goroutine
	go s.handleClientConnections()

	// 启动User连接处理goroutine
	go s.handleUserConnections()

	return nil
}

// handleClientConnections 处理Client连接
func (s *Socks5ReverseProxyServer) handleClientConnections() {
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			conn, err := s.clientListener.Accept()
			if err != nil {
				select {
				case <-s.ctx.Done():
					return
				default:
					global.LOG.Error("[-] Accept client error: ", zap.Error(err))
					continue
				}
			}
			global.LOG.Info("[+] Client connected")
			s.lock.Lock()
			if s.clientConn != nil {
				s.clientConn.Close() // 关闭旧连接
			}
			s.clientConn = conn
			s.lock.Unlock()
		}
	}
}

// handleUserConnections 处理User连接
func (s *Socks5ReverseProxyServer) handleUserConnections() {
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			conn, err := s.userListener.Accept()
			if err != nil {
				select {
				case <-s.ctx.Done():
					return
				default:
					global.LOG.Error("[-] Accept user error: ", zap.Error(err))
					continue
				}
			}
			global.LOG.Info("[+] User connected")
			go s.handleUserConnection(conn)
		}
	}
}

func (s *Socks5ReverseProxyServer) Stop() error {
	s.lock.Lock()
	if !s.running {
		s.lock.Unlock()
		return nil
	}
	s.running = false
	s.lock.Unlock()

	// 取消context，停止所有goroutine
	if s.cancel != nil {
		s.cancel()
	}

	// 关闭所有连接
	s.connectionManager.CloseAll()

	// 停止统计信息定期更新
	s.stats.stopPeriodicUpdate()

	// 更新代理状态和统计信息
	s.config.Status = 0 // 停止
	s.config.StoppedAt = time.Now()

	// 获取统计信息并保存到数据库
	totalConn, activeConn, bytesSent, bytesReceived, lastActivity := s.stats.GetStats()
	s.config.TotalConnections = totalConn
	s.config.ActiveConnections = int(activeConn)
	s.config.BytesTransferred = bytesSent
	s.config.BytesReceived = bytesReceived
	s.config.LastActivity = lastActivity

	// 🚀 最终保存统计信息到数据库
	updates := map[string]interface{}{
		"status":             s.config.Status,
		"stopped_at":         s.config.StoppedAt,
		"total_connections":  s.config.TotalConnections,
		"active_connections": s.config.ActiveConnections,
		"bytes_transferred":  s.config.BytesTransferred,
		"bytes_received":     s.config.BytesReceived,
		"last_activity":      s.config.LastActivity,
		"version":            s.config.Version + 1,
	}
	dbpool.ExecuteDBOperationAsyncAndWait("socks5_proxy_final_stats", func(db *gorm.DB) error {
		return db.Model(s.config.Proxy).Updates(updates).Error
	})

	var errs []error

	// 关闭 User 监听器
	if s.userListener != nil {
		if err := s.userListener.Close(); err != nil {
			errs = append(errs, fmt.Errorf("关闭 User 监听器失败: %v", err))
		}
	}

	// 关闭 Client 监听器
	if s.clientListener != nil {
		if err := s.clientListener.Close(); err != nil {
			errs = append(errs, fmt.Errorf("关闭 Client 监听器失败: %v", err))
		}
	}

	// 关闭 Client 连接
	s.lock.Lock()
	if s.clientConn != nil {
		if err := s.clientConn.Close(); err != nil {
			errs = append(errs, fmt.Errorf("关闭 Client 连接失败: %v", err))
		}
		s.clientConn = nil
	}
	s.lock.Unlock()

	// 返回错误信息（如果有的话）
	if len(errs) > 0 {
		return fmt.Errorf("停止服务时发生错误: %v", errs)
	}
	return nil
}

// handleUserConnection 处理用户连接，执行 SOCKS5 协议
func (s *Socks5ReverseProxyServer) handleUserConnection(userConn net.Conn) {
	defer userConn.Close()

	// 获取客户端IP
	clientIP := ""
	if addr, ok := userConn.RemoteAddr().(*net.TCPAddr); ok {
		clientIP = addr.IP.String()
	}

	// 访问控制检查
	if !s.accessController.IsAllowed(clientIP) {
		global.LOG.Warn("拒绝连接：IP不在允许列表中", zap.String("clientIP", clientIP))
		return
	}

	// 统计连接
	s.stats.AddConnection()
	defer s.stats.RemoveConnection()

	// 生成连接ID并添加到连接管理器
	connID := fmt.Sprintf("%s-%d", clientIP, time.Now().UnixNano())
	s.connectionManager.AddConnection(connID, userConn)
	defer s.connectionManager.RemoveConnection(connID)

	global.LOG.Info("用户连接已建立",
		zap.String("clientIP", clientIP),
		zap.String("connID", connID))

	// Step 1: 协议版本识别（SOCKS5）
	buf := make([]byte, 256)
	n, err := userConn.Read(buf)
	if err != nil || n < 2 {
		global.LOG.Error("[-] Handshake read error:", zap.Error(err))
		return
	}

	if buf[0] != 0x05 {
		global.LOG.Info(fmt.Sprintf("[-] Unsupported SOCKS version: %v", buf[0]))
		return
	}

	// Step 2: 方法协商（Method Selection）
	nMethods := int(buf[1])
	if n < 2+nMethods {
		global.LOG.Error("[-] Invalid method selection packet")
		return
	}
	methods := buf[2 : 2+nMethods]

	// 判断客户端是否支持 NO AUTHENTICATION REQUIRED (0x00)
	var supportsNoAuth bool
	for _, m := range methods {
		if m == 0x00 {
			supportsNoAuth = true
			break
		}
	}

	// 判断认证方法
	var selectedMethod byte = 0xFF // 默认不支持
	if s.config.AuthRequired {
		// 如果要求认证，必须使用用户名密码认证
		if s.config.Username != "" && s.config.Password != "" {
			// 检查客户端是否支持用户名密码认证
			for _, m := range methods {
				if m == 0x02 {
					selectedMethod = 0x02
					break
				}
			}
		}
		// 如果要求认证但没有配置用户名密码，或客户端不支持，则拒绝
	} else {
		// 不要求认证，优先使用无认证方法
		if supportsNoAuth {
			selectedMethod = 0x00
		} else {
			// 如果客户端不支持无认证，但配置了用户名密码，也可以使用
			if s.config.Username != "" && s.config.Password != "" {
				for _, m := range methods {
					if m == 0x02 {
						selectedMethod = 0x02
						break
					}
				}
			}
		}
	}

	// 如果没有找到支持的认证方法，拒绝连接
	if selectedMethod == 0xFF {
		_, _ = userConn.Write([]byte{0x05, 0xFF})
		global.LOG.Info("[-] No acceptable authentication methods")
		return
	}

	// 回复选择的认证方法
	_, err = userConn.Write([]byte{0x05, selectedMethod})
	if err != nil {
		global.LOG.Error("[-] Method selection reply error:", zap.Error(err))
		return
	}

	// 如果选择了用户名密码认证，则进入认证阶段
	if selectedMethod == 0x02 {
		if err := s.authenticateUser(userConn); err != nil {
			global.LOG.Error("[-] Authentication failed:", zap.Error(err))
			return
		}
	}

	// Step 3: 读取请求头
	n, err = io.ReadFull(userConn, buf[:4])
	if err != nil {
		global.LOG.Error("[-] Read request header error:", zap.Error(err))
		return
	}

	version, cmd, _, addrType := buf[0], buf[1], buf[2], buf[3]
	if version != 0x05 {
		global.LOG.Info("[-] Invalid SOCKS version in request")
		s.sendSocks5Reply(userConn, 0x05, 0x07) // Command not supported
		return
	}

	// Step 4: 解析目标地址
	var target string
	switch addrType {
	case 0x01: // IPv4
		ip := make([]byte, 4)
		_, err = io.ReadFull(userConn, ip)
		if err != nil {
			s.sendSocks5Reply(userConn, 0x05, 0x01)
			return
		}
		port := make([]byte, 2)
		_, err = io.ReadFull(userConn, port)
		if err != nil {
			s.sendSocks5Reply(userConn, 0x05, 0x01)
			return
		}
		target = fmt.Sprintf("%s:%d", net.IP(ip).String(), int(port[0])<<8|int(port[1]))

	case 0x03: // Domain
		domainLenBuf := make([]byte, 1)
		_, err = io.ReadFull(userConn, domainLenBuf)
		if err != nil {
			s.sendSocks5Reply(userConn, 0x05, 0x01)
			return
		}
		domainLen := domainLenBuf[0]
		domain := make([]byte, domainLen)
		_, err = io.ReadFull(userConn, domain)
		if err != nil {
			s.sendSocks5Reply(userConn, 0x05, 0x01)
			return
		}
		port := make([]byte, 2)
		_, err = io.ReadFull(userConn, port)
		if err != nil {
			s.sendSocks5Reply(userConn, 0x05, 0x01)
			return
		}
		target = fmt.Sprintf("%s:%d", string(domain), int(port[0])<<8|int(port[1]))

	case 0x04: // IPv6
		global.LOG.Info("[-] IPv6 address not supported")
		s.sendSocks5Reply(userConn, 0x05, 0x08) // Address type not supported
		return

	default:
		global.LOG.Info("[-] Unsupported address type")
		s.sendSocks5Reply(userConn, 0x05, 0x08)
		return
	}

	// Step 5: 仅处理 CONNECT 命令
	if cmd != 0x01 {
		global.LOG.Info(fmt.Sprintf("[-] Unsupported command: %v", cmd))
		s.sendSocks5Reply(userConn, 0x05, 0x07) // Command not supported
		return
	}

	// Step 6: 响应成功（0x00）
	s.sendSocks5Reply(userConn, 0x05, 0x00)

	// Step 7: 获取 Client 连接并发送目标地址
	s.lock.Lock()
	clientConn := s.clientConn
	s.lock.Unlock()

	if clientConn == nil {
		global.LOG.Info("[-] No client connected")
		s.sendSocks5Reply(userConn, 0x05, 0x01) // General failure
		return
	}

	// 自定义协议：发送目标地址长度 + 地址内容
	_, err = clientConn.Write([]byte{byte(len(target))})
	if err != nil {
		global.LOG.Error("[-] Send target length error:", zap.Error(err))
		return
	}
	_, err = clientConn.Write([]byte(target))
	if err != nil {
		global.LOG.Error("[-] Send target error:", zap.Error(err))
		return
	}

	// Step 8: 建立双向转发
	global.LOG.Info(fmt.Sprintf("[+] Forwarding traffic to %s\n", target))

	// 使用带统计功能的数据转发
	done := make(chan struct{}, 2)

	// 从 User 到 Client
	go func() {
		defer func() { done <- struct{}{} }()
		sent, _ := io.Copy(clientConn, userConn)
		s.stats.AddBytes(sent, 0)
	}()

	// 从 Client 到 User
	go func() {
		defer func() { done <- struct{}{} }()
		received, _ := io.Copy(userConn, clientConn)
		s.stats.AddBytes(0, received)
	}()

	// 等待任一方向的转发结束
	<-done
}

// authenticateUser 处理用户名密码认证
func (s *Socks5ReverseProxyServer) authenticateUser(conn net.Conn) error {
	// 读取版本
	buf := make([]byte, 256)
	_, err := io.ReadFull(conn, buf[:1])
	if err != nil || buf[0] != 0x01 {
		return errors.New("不支持的认证版本")
	}

	// 读取用户名长度
	_, err = io.ReadFull(conn, buf[:1])
	if err != nil {
		return err
	}
	userLen := int(buf[0])

	// 读取用户名
	_, err = io.ReadFull(conn, buf[:userLen])
	if err != nil {
		return err
	}
	username := string(buf[:userLen])

	// 读取密码长度
	_, err = io.ReadFull(conn, buf[:1])
	if err != nil {
		return err
	}
	passLen := int(buf[0])

	// 读取密码
	_, err = io.ReadFull(conn, buf[:passLen])
	if err != nil {
		return err
	}
	password := string(buf[:passLen])

	// 验证用户名和密码
	if username != s.config.Username || password != s.config.Password {
		// 回复认证失败
		_, _ = conn.Write([]byte{0x01, 0x01}) // 0x01 = 失败
		return errors.New("认证失败")
	}

	// 回复认证成功
	_, _ = conn.Write([]byte{0x01, 0x00}) // 0x00 = 成功
	return nil
}

func (s *Socks5ReverseProxyServer) sendSocks5Reply(conn net.Conn, version, code byte) {
	// 获取连接的本地地址
	localAddr := conn.LocalAddr().(*net.TCPAddr)
	ip := localAddr.IP
	port := localAddr.Port

	// 确保使用 IPv4 地址
	if ip.To4() == nil {
		ip = net.IPv4zero
	}

	_, _ = conn.Write([]byte{
		version, // 版本号
		code,    // 应答码
		0x00,    // 保留字段
		0x01,    // 地址类型 IPv4
		ip.To4()[0], ip.To4()[1], ip.To4()[2], ip.To4()[3],
		byte(port >> 8), byte(port & 0xFF), // 真实端口
	})
}

// StopServer 停止指定端口的 Socks5 服务
func (m *Socks5Manager) StopServer(proxy *basic.Proxy) error {
	m.lock.Lock()
	defer m.lock.Unlock()

	server, exists := m.servers[proxy]
	if !exists {
		return nil
	}

	// 调用 Stop() 方法
	if err := server.Stop(); err != nil {
		return err
	}

	// 从 map 中删除
	delete(m.servers, proxy)

	return nil
}

func (m *Socks5Manager) handleSocks5Connection(clientConn net.Conn, userConn net.Conn) {
	go io.Copy(clientConn, userConn) // 用户 -> Client socks5
	go io.Copy(userConn, clientConn) // Client socks5 -> 用户
}
