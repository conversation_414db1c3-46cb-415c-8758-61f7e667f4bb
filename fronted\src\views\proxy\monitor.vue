<template>
  <div class="proxy-monitor-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>代理监控统计</h2>
        <p class="header-desc">实时监控代理系统的运行状态和性能指标</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button @click="refreshData">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-switch v-model:checked="autoRefresh" checked-children="自动刷新" un-checked-children="手动刷新" />
        </a-space>
      </div>
    </div>

    <!-- 系统概览 -->
    <a-card title="系统概览" class="overview-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic
            title="代理总数"
            :value="dashboard.overview?.total_proxies || 0"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <ApiOutlined />
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="运行中"
            :value="dashboard.overview?.active_proxies || 0"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <PlayCircleOutlined />
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="总连接数"
            :value="dashboard.overview?.total_connections || 0"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <LinkOutlined />
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="活跃连接"
            :value="dashboard.overview?.active_connections || 0"
            :value-style="{ color: '#fa8c16' }"
          >
            <template #prefix>
              <ThunderboltOutlined />
            </template>
          </a-statistic>
        </a-col>
      </a-row>

      <a-divider />

      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic
            title="总流量"
            :value="formatBytes(dashboard.overview?.total_traffic || 0)"
            :value-style="{ color: '#13c2c2' }"
          >
            <template #prefix>
              <CloudOutlined />
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="平均延迟"
            :value="dashboard.overview?.avg_latency || 0"
            suffix="ms"
            :value-style="{ color: '#eb2f96' }"
          >
            <template #prefix>
              <ClockCircleOutlined />
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="错误率"
            :value="((dashboard.overview?.error_rate || 0) * 100).toFixed(2)"
            suffix="%"
            :value-style="{ color: dashboard.overview?.error_rate > 0.05 ? '#f5222d' : '#52c41a' }"
          >
            <template #prefix>
              <ExclamationCircleOutlined />
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="代理链数"
            :value="dashboard.overview?.total_chains || 0"
            :value-style="{ color: '#2f54eb' }"
          >
            <template #prefix>
              <NodeIndexOutlined />
            </template>
          </a-statistic>
        </a-col>
      </a-row>
    </a-card>

    <!-- 性能排行榜 -->
    <a-row :gutter="16" style="margin-top: 16px">
      <a-col :span="12">
        <a-card title="性能排行榜" :bordered="false">
          <a-list
            :data-source="dashboard.top_proxies || []"
            :loading="loading"
          >
            <template #renderItem="{ item, index }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-badge :count="index + 1" :number-style="{ backgroundColor: getRankColor(index) }">
                      <a-avatar :style="{ backgroundColor: getProxyTypeColor(item.type) }">
                        {{ getProxyTypeText(item.type).charAt(0) }}
                      </a-avatar>
                    </a-badge>
                  </template>
                  <template #title>
                    <span>{{ item.name }}</span>
                    <a-tag size="small" :color="getProxyTypeColor(item.type)" style="margin-left: 8px">
                      {{ getProxyTypeText(item.type) }}
                    </a-tag>
                  </template>
                  <template #description>
                    <div class="proxy-stats">
                      <span>连接: {{ item.connections || 0 }}</span>
                      <a-divider type="vertical" />
                      <span>流量: {{ formatBytes(item.traffic || 0) }}</span>
                      <a-divider type="vertical" />
                      <span>延迟: {{ item.latency || 0 }}ms</span>
                    </div>
                  </template>
                </a-list-item-meta>
                <div class="score">
                  <a-progress
                    type="circle"
                    :percent="item.score || 0"
                    :width="50"
                    :stroke-color="getScoreColor(item.score)"
                  />
                </div>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>

      <a-col :span="12">
        <a-card title="最近活动" :bordered="false">
          <a-timeline>
            <a-timeline-item
              v-for="activity in dashboard.recent_activity || []"
              :key="activity.id"
              :color="getActivityColor(activity.severity)"
            >
              <template #dot>
                <component :is="getActivityIcon(activity.type)" />
              </template>
              <div class="activity-item">
                <div class="activity-message">{{ activity.message }}</div>
                <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </a-col>
    </a-row>

    <!-- 告警信息 -->
    <a-card v-if="dashboard.alerts && dashboard.alerts.length > 0" title="告警信息" style="margin-top: 16px" :bordered="false">
      <a-alert
        v-for="alert in dashboard.alerts"
        :key="alert.id"
        :message="alert.title"
        :description="alert.message"
        :type="alert.level"
        show-icon
        :closable="true"
        style="margin-bottom: 8px"
      />
    </a-card>

    <!-- 图表展示区域 -->
    <a-row :gutter="16" style="margin-top: 16px">
      <a-col :span="12">
        <a-card title="连接数趋势" :bordered="false">
          <div class="chart-placeholder">
            <a-empty description="图表功能开发中..." />
          </div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="流量趋势" :bordered="false">
          <div class="chart-placeholder">
            <a-empty description="图表功能开发中..." />
          </div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" style="margin-top: 16px">
      <a-col :span="12">
        <a-card title="延迟分布" :bordered="false">
          <div class="chart-placeholder">
            <a-empty description="图表功能开发中..." />
          </div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="错误统计" :bordered="false">
          <div class="chart-placeholder">
            <a-empty description="图表功能开发中..." />
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  ApiOutlined,
  PlayCircleOutlined,
  LinkOutlined,
  ThunderboltOutlined,
  CloudOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  NodeIndexOutlined,
  BellOutlined,
  CheckCircleOutlined,
  WarningOutlined
} from '@ant-design/icons-vue'
import { getProxyDashboard } from '@/api/proxy'
import { formatBytes, formatTime } from '@/utils/format'

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(true)
const dashboard = ref({})
let refreshTimer = null

// 方法
const getProxyTypeColor = (type) => {
  const colors = {
    forward: '#52c41a',
    reverse: '#fa8c16',
    chain: '#1890ff'
  }
  return colors[type] || '#d9d9d9'
}

const getProxyTypeText = (type) => {
  const texts = {
    forward: '正向',
    reverse: '反向',
    chain: '链式'
  }
  return texts[type] || type
}

const getRankColor = (index) => {
  const colors = ['#f5222d', '#fa8c16', '#fadb14', '#52c41a', '#1890ff']
  return colors[index] || '#d9d9d9'
}

const getScoreColor = (score) => {
  if (score >= 90) return '#52c41a'
  if (score >= 70) return '#fadb14'
  if (score >= 50) return '#fa8c16'
  return '#f5222d'
}

const getActivityColor = (severity) => {
  const colors = {
    info: 'blue',
    success: 'green',
    warning: 'orange',
    error: 'red'
  }
  return colors[severity] || 'blue'
}

const getActivityIcon = (type) => {
  const icons = {
    proxy_activity: CheckCircleOutlined,
    proxy_error: WarningOutlined,
    proxy_alert: BellOutlined
  }
  return icons[type] || CheckCircleOutlined
}

const loadDashboard = async () => {
  loading.value = true
  try {
    const response = await getProxyDashboard()
    if (response.code === 200) {
      dashboard.value = response.data || {}
    } else {
      message.error(response.msg || '获取仪表板数据失败')
    }
  } catch (error) {
    console.error('获取仪表板数据失败:', error)
    message.error('获取仪表板数据失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadDashboard()
}

const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  if (autoRefresh.value) {
    refreshTimer = setInterval(() => {
      loadDashboard()
    }, 30000) // 30秒刷新一次
  }
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 监听自动刷新开关
watch(() => autoRefresh.value, (newVal) => {
  if (newVal) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

// 生命周期
onMounted(() => {
  loadDashboard()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.proxy-monitor-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.overview-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.proxy-stats {
  color: #8c8c8c;
  font-size: 12px;
}

.score {
  display: flex;
  align-items: center;
}

.activity-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.activity-message {
  font-size: 14px;
  color: #262626;
}

.activity-time {
  font-size: 12px;
  color: #8c8c8c;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #8c8c8c;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}

:deep(.ant-list-item-meta-title) {
  margin-bottom: 4px;
}

:deep(.ant-timeline-item-content) {
  margin-left: 8px;
}
</style>
