//go:build darwin && !cgo
// +build darwin,!cgo

package common

import (
	"log"
)

// 处理截图请求的主函数（非CGO版本，用于交叉编译）
func (cm *ConnectionManager) handleScreenshotRequest(packet *Packet) {
	// 非CGO版本：简化实现，记录日志但不执行截图功能
	switch packet.Header.Code {
	case Pic:
		log.Printf("截图功能在非CGO版本中不可用")
	case StreamStart:
		log.Printf("截图流开始功能在非CGO版本中不可用")
	case StreamStop:
		log.Printf("截图流停止功能在非CGO版本中不可用")
	case StreamData:
		log.Printf("截图流数据功能在非CGO版本中不可用")
	case MonitorList:
		log.Printf("显示器列表功能在非CGO版本中不可用")
	default:
		log.Printf("未知的截图操作代码: %d", packet.Header.Code)
	}
}
