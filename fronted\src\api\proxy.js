import request from '@/utils/request'

// 代理实例管理API

/**
 * 测试Server端口可用性
 * @param {Object} data - 测试参数
 * @param {number} data.port - 要测试的端口
 * @param {string} data.type - 代理类型 (forward/reverse/chain)
 * @returns {Promise}
 */
export function checkServerPortAvailability(data) {
  return request({
    url: '/proxy/check-server-port',
    method: 'post',
    data
  })
}

/**
 * 测试Client端口可用性
 * @param {Object} data - 测试参数
 * @param {number} data.port - 要测试的端口
 * @param {number} data.client_id - 客户端ID
 * @param {string} data.type - 代理类型 (forward/reverse/chain)
 * @returns {Promise}
 */
export function checkClientPortAvailability(data) {
  return request({
    url: '/proxy/check-client-port',
    method: 'post',
    data
  })
}

/**
 * 获取代理实例列表
 * @param {Object} data - 查询参数
 * @param {number} data.page - 页码
 * @param {number} data.page_size - 每页数量
 * @param {string} data.proxy_id - 代理ID过滤
 * @param {string} data.name - 名称过滤
 * @param {string} data.type - 类型过滤
 * @param {number} data.status - 状态过滤
 * @param {number} data.client_id - 客户端ID过滤
 * @param {string} data.chain_id - 代理链ID过滤
 * @returns {Promise}
 */
export function getProxyList(data) {
  return request({
    url: '/proxy/list',
    method: 'post',
    data
  })
}

/**
 * 获取代理实例详情
 * @param {number} id - 代理实例ID
 * @returns {Promise}
 */
export function getProxyDetail(id) {
  return request({
    url: `/proxy/${id}`,
    method: 'get'
  })
}

/**
 * 创建代理实例
 * @param {Object} data - 代理实例数据
 * @param {string} data.name - 代理名称
 * @param {string} data.description - 代理描述
 * @param {string} data.type - 代理类型 (forward/reverse/chain)
 * @param {number} data.port - 监听端口
 * @param {boolean} data.auth_required - 是否需要认证
 * @param {string} data.username - 认证用户名
 * @param {string} data.password - 认证密码
 * @param {number} data.max_connections - 最大连接数
 * @param {number} data.timeout - 超时时间
 * @param {number} data.buffer_size - 缓冲区大小
 * @param {number} data.client_id - 客户端ID
 * @param {number} data.listener_id - 监听器ID
 * @param {string} data.chain_id - 代理链ID
 * @param {string} data.allowed_ips - 允许的IP列表
 * @param {string} data.blocked_ips - 阻止的IP列表
 * @param {number} data.rate_limit_rps - 速率限制(请求/秒)
 * @param {number} data.rate_limit_bytes - 带宽限制(字节/秒)
 * @param {string} data.log_level - 日志级别
 * @param {boolean} data.enable_metrics - 是否启用指标收集
 * @returns {Promise}
 */
export function createProxy(data) {
  return request({
    url: '/proxy/create',
    method: 'post',
    data
  })
}

/**
 * 更新代理实例
 * @param {Object} data - 更新数据
 * @param {number} data.id - 代理实例ID
 * @returns {Promise}
 */
export function updateProxy(data) {
  return request({
    url: '/proxy/update',
    method: 'put',
    data
  })
}

/**
 * 删除代理实例
 * @param {number} id - 代理实例ID
 * @returns {Promise}
 */
export function deleteProxy(id) {
  return request({
    url: `/proxy/delete/${id}`,
    method: 'delete'
  })
}

/**
 * 控制代理实例
 * @param {Object} data - 控制数据
 * @param {string} data.proxy_id - 代理ID
 * @param {string} data.action - 操作类型 (start/stop/restart)
 * @returns {Promise}
 */
export function controlProxy(data) {
  return request({
    url: '/proxy/control',
    method: 'post',
    data
  })
}

/**
 * 批量代理操作
 * @param {Object} data - 批量操作数据
 * @param {Array} data.proxy_ids - 代理ID列表
 * @param {string} data.action - 操作类型 (start/stop/restart/delete)
 * @returns {Promise}
 */
export function batchProxyOperation(data) {
  return request({
    url: '/proxy/batch',
    method: 'post',
    data
  })
}

/**
 * 获取代理连接列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getProxyConnections(params) {
  return request({
    url: '/proxy/connections',
    method: 'get',
    params
  })
}

/**
 * 获取代理统计信息
 * @param {Object} params - 查询参数
 * @param {string} params.proxy_id - 代理ID
 * @param {string} params.chain_id - 代理链ID
 * @param {string} params.start_time - 开始时间
 * @param {string} params.end_time - 结束时间
 * @param {string} params.granularity - 时间粒度
 * @returns {Promise}
 */
export function getProxyStats(params) {
  return request({
    url: '/proxy/stats',
    method: 'get',
    params
  })
}

/**
 * 获取代理指标数据
 * @param {Object} params - 查询参数
 * @param {string} params.proxy_id - 代理ID
 * @param {string} params.chain_id - 代理链ID
 * @param {string} params.metric_type - 指标类型
 * @param {string} params.start_time - 开始时间
 * @param {string} params.end_time - 结束时间
 * @param {string} params.granularity - 时间粒度
 * @returns {Promise}
 */
export function getProxyMetrics(params) {
  return request({
    url: '/proxy/metrics',
    method: 'get',
    params
  })
}

/**
 * 获取代理健康状态
 * @param {Object} params - 查询参数
 * @param {string} params.proxy_ids - 代理ID列表
 * @param {string} params.chain_ids - 代理链ID列表
 * @param {boolean} params.force - 是否强制检查
 * @returns {Promise}
 */
export function getProxyHealth(params) {
  return request({
    url: '/proxy/health',
    method: 'get',
    params
  })
}

/**
 * 获取代理仪表板数据
 * @returns {Promise}
 */
export function getProxyDashboard() {
  return request({
    url: '/proxy/dashboard',
    method: 'get'
  })
}

// 代理链管理API

/**
 * 获取代理链列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getProxyChainList(params) {
  return request({
    url: '/proxy-chain',
    method: 'get',
    params
  })
}

/**
 * 获取代理链详情
 * @param {number} id - 代理链ID
 * @returns {Promise}
 */
export function getProxyChainDetail(id) {
  return request({
    url: `/proxy-chain/${id}`,
    method: 'get'
  })
}

/**
 * 创建代理链
 * @param {Object} data - 代理链数据
 * @returns {Promise}
 */
export function createProxyChain(data) {
  return request({
    url: '/proxy-chain',
    method: 'post',
    data
  })
}

/**
 * 更新代理链
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function updateProxyChain(data) {
  return request({
    url: '/proxy-chain',
    method: 'put',
    data
  })
}

/**
 * 删除代理链
 * @param {number} id - 代理链ID
 * @returns {Promise}
 */
export function deleteProxyChain(id) {
  return request({
    url: `/proxy-chain/${id}`,
    method: 'delete'
  })
}

/**
 * 控制代理链
 * @param {Object} data - 控制数据
 * @param {string} data.chain_id - 代理链ID
 * @param {string} data.action - 操作类型 (start/stop/restart/optimize)
 * @returns {Promise}
 */
export function controlProxyChain(data) {
  return request({
    url: '/proxy-chain/control',
    method: 'post',
    data
  })
}

/**
 * 添加代理链节点
 * @param {Object} data - 节点数据
 * @returns {Promise}
 */
export function addProxyChainNode(data) {
  return request({
    url: '/proxy-chain/node',
    method: 'post',
    data
  })
}

/**
 * 更新代理链节点
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function updateProxyChainNode(data) {
  return request({
    url: '/proxy-chain/node',
    method: 'put',
    data
  })
}

/**
 * 移除代理链节点
 * @param {Object} data - 移除数据
 * @param {string} data.chain_id - 代理链ID
 * @param {string} data.node_id - 节点ID
 * @returns {Promise}
 */
export function removeProxyChainNode(data) {
  return request({
    url: '/proxy-chain/node',
    method: 'delete',
    data
  })
}

/**
 * 获取代理链节点列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getProxyChainNodes(params) {
  return request({
    url: '/proxy-chain/nodes',
    method: 'get',
    params
  })
}
