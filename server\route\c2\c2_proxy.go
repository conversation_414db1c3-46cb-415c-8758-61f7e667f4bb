package c2

import "github.com/gin-gonic/gin"

type ProxyRoute struct{}

func (p *ProxyRoute) InitProxyRoute(Router *gin.RouterGroup) {
	proxyRouter := Router.Group("proxy")
	{
		proxyRouter.POST("/check-server-port", proxyApi.CheckServerPortAvailability)
		proxyRouter.POST("/check-client-port", proxyApi.CheckClientPortAvailability)
		proxyRouter.GET("/:id", proxyApi.GetProxyInstance)
		proxyRouter.POST("/list", proxyApi.ListProxy)
		proxyRouter.POST("/create", proxyApi.CreateProxy)
		proxyRouter.POST("/control", proxyApi.ControlProxy)
		proxyRouter.DELETE("/delete/:clientId", proxyApi.DeleteProxy)
		proxyRouter.PUT("/update", proxyApi.UpdateProxy)
	}
}
